import { ElMessage } from 'element-plus';

// 存储当前活动的消息实例
let activeMessages = [];
let messageIdCounter = 0;

// 消息配置
const MESSAGE_CONFIG = {
  baseOffset: 100, // 基础距离顶部的距离
  messageHeight: 60, // 每个消息的高度（包括间距）
  baseZIndex: 3000, // 基础 z-index
  maxMessages: 5, // 最大同时显示的消息数量
  duration: 3000, // 默认显示时间
};

// 创建自定义消息服务
const messageService = {
  // 计算消息的 offset
  calculateOffset(index) {
    return MESSAGE_CONFIG.baseOffset + (index * MESSAGE_CONFIG.messageHeight);
  },

  // 计算消息的 z-index
  calculateZIndex(index) {
    return MESSAGE_CONFIG.baseZIndex + index;
  },

  // 更新所有消息的位置
  updateMessagePositions() {
    activeMessages.forEach((msg, index) => {
      if (msg.instance && msg.instance.$el) {
        const offset = this.calculateOffset(index);
        const zIndex = this.calculateZIndex(activeMessages.length - 1 - index); // 最新的在最上面

        msg.instance.$el.style.top = `${offset}px`;
        msg.instance.$el.style.zIndex = zIndex;
      }
    });
  },

  // 移除消息
  removeMessage(messageId) {
    const index = activeMessages.findIndex(msg => msg.id === messageId);
    if (index !== -1) {
      activeMessages.splice(index, 1);
      this.updateMessagePositions();
    }
  },

  // 显示消息
  show(options) {
    // 如果达到最大数量，移除最旧的消息
    if (activeMessages.length >= MESSAGE_CONFIG.maxMessages) {
      const oldestMessage = activeMessages[0];
      if (oldestMessage.instance && oldestMessage.instance.close) {
        oldestMessage.instance.close();
      }
      activeMessages.shift();
    }

    const messageId = ++messageIdCounter;
    const messageIndex = activeMessages.length;

    // 检查消息长度，决定是否需要特殊样式
    const messageText = options.message || '';
    const isLongMessage = messageText.length > 50; // 超过50个字符认为是长消息

    // 准备消息配置
    const messageOptions = {
      duration: MESSAGE_CONFIG.duration,
      showClose: false, // 隐藏右侧关闭按钮
      offset: this.calculateOffset(messageIndex),
      customClass: isLongMessage ? 'el-message--long' : '',
      ...options,
      onClose: () => {
        this.removeMessage(messageId);
        if (options.onClose) {
          options.onClose();
        }
      }
    };

    // 创建消息实例
    const instance = ElMessage(messageOptions);

    // 存储消息信息
    const messageInfo = {
      id: messageId,
      instance: instance,
      timestamp: Date.now()
    };

    activeMessages.push(messageInfo);

    // 设置 z-index（最新的在最上面）
    setTimeout(() => {
      if (instance.$el) {
        const zIndex = this.calculateZIndex(activeMessages.length - 1);
        instance.$el.style.zIndex = zIndex;
      }
    }, 0);

    return instance;
  },

  // 成功消息
  success(message, options = {}) {
    return this.show({
      message,
      type: 'success',
      ...options
    });
  },

  // 警告消息
  warning(message, options = {}) {
    return this.show({
      message,
      type: 'warning',
      ...options
    });
  },

  // 错误消息
  error(message, options = {}) {
    return this.show({
      message,
      type: 'error',
      ...options
    });
  },

  // 信息消息
  info(message, options = {}) {
    return this.show({
      message,
      type: 'info',
      ...options
    });
  },

  // 主要消息
  primary(message, options = {}) {
    return this.show({
      message,
      type: 'primary',
      ...options
    });
  },

  // 关闭所有消息
  closeAll() {
    activeMessages.forEach(msg => {
      if (msg.instance && msg.instance.close) {
        msg.instance.close();
      }
    });
    activeMessages = [];
  }
};

// 导出服务
export default messageService;

// Vue插件
export const messagePlugin = {
  install(app) {
    app.config.globalProperties.$message = messageService;
  }
};
