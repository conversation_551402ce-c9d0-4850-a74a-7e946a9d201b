/* 覆盖 el-tooltip 的样式 */
/* .el-tooltip__popper {
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
} */

/* 隐藏箭头 */
.el-popper__arrow {
  display: none !important;
}

/* styles/element-plus.css */
.el-message {
  position: fixed !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  transition: all 0.3s ease !important;
  margin: 0 !important;
  width: auto !important;
  min-width: 200px !important;
  max-width: 80vw !important;
  padding: 12px 16px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 消息内容自适应 */
.el-message__content {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 对于较长的消息，允许换行 */
.el-message.el-message--long {
  white-space: normal !important;
  max-width: 60vw !important;
}

.el-message.el-message--long .el-message__content {
  white-space: normal !important;
  word-wrap: break-word !important;
}

/* 确保多个消息不会重叠 */
.el-message + .el-message {
  margin-top: 0 !important;
}

/* 消息动画优化 */
.el-message.el-message--fade-enter-active,
.el-message.el-message--fade-leave-active {
  transition: all 0.3s ease !important;
}

.el-message.el-message--fade-enter-from {
  opacity: 0 !important;
  transform: translateX(-50%) translateY(-20px) !important;
}

.el-message.el-message--fade-leave-to {
  opacity: 0 !important;
  transform: translateX(-50%) translateY(-20px) !important;
}
