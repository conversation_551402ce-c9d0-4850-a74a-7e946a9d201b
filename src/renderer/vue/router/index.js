import { createRouter, createWebHashHistory } from 'vue-router';
// 导入流媒体服务
import { stopAllScreenCasts } from '../services/stream-service';
import { useElectronAPI } from '../plugins/electron';
import alertService from '../plugins/alert';
import messageService from '../plugins/message';

// 导入页面组件
// 使用动态导入实现代码分割和懒加载
const DevelopingPage = () => import('../views/DevelopingPage.vue');
const DevicesPage = () => import('../views/DevicesPage.vue');
const AddDevicePage = () => import('../views/AddDevicePage.vue');
const ControlPage = () => import('../views/ControlPage.vue');
const SolutionsPage = () => import('../views/SolutionsPage.vue');
const SettingsPage = () => import('../views/SettingsPage.vue');

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/devices'
  },
  {
    path: '/devices',
    name: 'Devices',
    component: DevicesPage
  },
  {
    path: '/add-device',
    name: 'AddDevice',
    component: AddDevicePage
  },
  {
    path: '/control',
    name: 'Control',
    component: ControlPage
  },
  {
    path: '/solutions',
    name: 'Solutions',
    component: SolutionsPage,
    beforeEnter: async (to, from, next) => {
      try {
        const electronAPI = useElectronAPI();
        const records = await electronAPI.getPublishRecords();
        if (records && records.length > 0) {
          messageService.success('当前有正在播放的内容，请先停止播放后再进入方案管理。');
          next(false); // 阻止导航
        } else {
          next(); // 允许导航
        }
      } catch (error) {
        console.error('检查播放记录失败:', error);
        next(); // 发生错误时允许导航
      }
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: SettingsPage
  }
];

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes
});

// 添加全局前置守卫
router.beforeEach((to, from, next) => {
  // 如果从设备页面离开，停止所有投屏
  if (from.path === '/devices' && to.path !== '/devices') {
    try {
      console.log('检测到页面切换，在后台停止所有投屏');
      // 使用非阻塞方式停止投屏，不等待完成
      stopAllScreenCasts(false, 100); // 不等待完成，每个停止操作之间延迟100毫秒
    } catch (error) {
      console.error('停止所有投屏失败:', error);
    }
  }

  // 立即进行页面切换，不等待投屏停止完成
  next();
});

export default router;
