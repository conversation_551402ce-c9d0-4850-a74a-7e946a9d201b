<template>
  <div class="resource-dialog">
    <div class="btn-close" @click="$emit('close')">
      <img src="@assets/svg/close.svg" alt="" />
    </div>
    <div class="dialog-header">
      {{ isEdit ? "编辑" : "添加"
      }}{{ MainTypeLabels[getMainFileType(formData.type)] }}
    </div>
    <form class="form-content" @submit.prevent="handleSubmit">
      <!-- 文件上传区域（添加模式和编辑模式都显示） -->
      <div class="form-group required">
        <div class="file-upload-box" :class="{ 'has-file': formData.filePath }">
          <div v-if="formData.filePath" class="file-info">
            <span class="file-name">{{ getFileName(formData.filePath) }}</span>
            <div type="button" class="remove-file-btn" @click.stop="removeFile">
              ×
            </div>
          </div>
          <template v-else>
            <span>请上传{{ getFileTypeText() }}</span>
            <button type="button" class="upload-btn" @click.stop="selectFile">
              选择文件
            </button>
          </template>
        </div>
      </div>
      <div class="form-group required">
        <input
          type="text"
          id="resource-name"
          v-model="formData.showName"
          required
          placeholder="请输入资源名称"
        />
      </div>
      <div class="form-group">
        <textarea
          id="resource-description"
          v-model="formData.describe"
          placeholder="请输入资源简介（可选）"
        ></textarea>
      </div>

      <!-- 子类型选择（编辑模式和添加模式都显示） -->
      <div v-if="availableSubTypes.length > 1" class="form-group">
        <div class="sub-type-selector">
          <select
            v-model="formData.type"
            class="sub-type-select"
            @change="handleSubTypeChange"
          >
            <option
              v-for="typeValue in availableSubTypes"
              :key="typeValue"
              :value="typeValue"
            >
              {{ TypeLabels[typeValue] }}
            </option>
          </select>
        </div>
      </div>

      <!-- 封面上传区域（添加模式和编辑模式都显示） -->
      <div class="form-group">
        <div
          class="file-upload-box"
          :class="{ 'has-file': formData.coverPath }"
        >
          <div v-if="formData.coverPath" class="file-info">
            <span class="file-name">{{ getFileName(formData.coverPath) }}</span>
            <div
              type="button"
              class="remove-file-btn"
              @click.stop="removeCover"
            >
              ×
            </div>
          </div>
          <template v-else>
            <span>请上传封面图片（可选）</span>
            <button type="button" class="upload-btn" @click.stop="selectCover">
              选择图片
            </button>
          </template>
        </div>
      </div>

      <div class="form-group required">
        <div class="categories-container">
          <div
            v-for="category in categories"
            :key="category"
            class="category-radio"
          >
            <input
              type="radio"
              :id="`category-${category}`"
              name="category"
              :value="category"
              v-model="formData.category"
              required
            />
            <label :for="`category-${category}`">{{ category }}</label>
          </div>
        </div>
      </div>
      <div class="dialog-footer">
        <button type="submit" class="save-btn">确定</button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import {
  FileType,
  TypeLabels,
  FileExtensions,
  FileTypeKeys,
  KeyToFileType,
  MainFileType,
  MainTypeLabels,
  getMainFileType,
  getFileTypesByMainType,
} from "../../../shared/types/resource.js";
import { useElectronAPI } from "../plugins/electron";
import alertService from "../plugins/alert";
import { DEFAULT_GROUPS } from "../../../shared/constants/defaults.js";
import messageService from "../plugins/message";

const electronAPI = useElectronAPI();

// Props 定义
const props = defineProps({
  // 要编辑的资源对象，如果为空则为添加模式
  resource: {
    type: Object,
    default: () => ({}),
  },
  // 可用的分类列表
  categories: {
    type: Array,
    default: () => ["播控方案"],
  },
  // 当前选中的分类（用于添加模式）
  selectedCategory: {
    type: String,
    default: "",
  },
  // 选中的资源类型（用于添加模式）
  selectedType: {
    type: String,
    default: MainFileType.VIDEO,
    validator: (value) => {
      return Object.values(MainFileType).includes(value);
    },
  },
});

// 定义常量 - 已选择的文件类型中文名称
const selectedTypeName = computed(() => {
  return MainTypeLabels[props.selectedType] || "未知类型";
});

// 定义事件
const emit = defineEmits(["save", "close"]);

// 计算属性 - 是否为编辑模式
const isEdit = computed(() => {
  return (
    Object.keys(props.resource).length > 0 && props.resource.index !== undefined
  );
});

// 获取主类型对应的默认子类型
const getDefaultTypeForMainType = (mainType) => {
  switch (mainType) {
    case MainFileType.VIDEO:
      return FileType.VIDEO_2D_PLANE;
    case MainFileType.IMAGE:
      return FileType.IMAGE_2D_PLANE;
    case MainFileType.APP:
      return FileType.APP;
    default:
      return FileType.VIDEO_2D_PLANE;
  }
};

// 当前主类型
const currentMainType = computed(() => {
  return isEdit.value
    ? getMainFileType(formData.value.type)
    : props.selectedType;
});

// 计算属性 - 可用的子类型
const availableSubTypes = computed(() => {
  return getFileTypesByMainType(currentMainType.value);
});

// 表单数据
const formData = ref({
  showName: props.resource.showName || "",
  describe: props.resource.describe || "",
  category:
    props.resource.groups?.[0] ||
    (props.selectedCategory &&
    props.selectedCategory !== "全部" &&
    props.categories.includes(props.selectedCategory)
      ? props.selectedCategory
      : props.categories.includes("默认")
        ? "默认"
        : props.categories.length > 0
          ? props.categories[0]
          : ""),
  filePath: "",
  coverPath: "",
  type: isEdit.value
    ? props.resource.type === FileType.OTHER
      ? FileType.VIDEO_2D_PLANE
      : props.resource.type
    : getDefaultTypeForMainType(props.selectedType),
});

// 监听 selectedType 的变化
watch(
  () => props.selectedType,
  (newType) => {
    if (!isEdit.value) {
      formData.value.type = getDefaultTypeForMainType(newType);

      // 如果是从视频类型切换，清空封面
      if (newType === MainFileType.VIDEO && formData.value.coverPath) {
        formData.value.coverPath = "";
      }
    }
  }
);

// 方法 - 处理子类型变化
const handleSubTypeChange = () => {
  console.log("子类型变化 -", formData.value.type);

  // 如果是视频类型且有封面，清空封面
  if (
    currentMainType.value === MainFileType.VIDEO &&
    formData.value.coverPath
  ) {
    console.log("清空视频封面");
    formData.value.coverPath = "";
  }
};

// 方法 - 获取文件名
const getFileName = (path) => {
  if (!path) return "";

  try {
    // 如果 path 是对象且有 name 属性，直接返回 name
    if (typeof path === "object" && path !== null) {
      if (path.name) {
        return path.name;
      }

      // 如果有 path 属性，尝试从中提取文件名
      if (path.path && typeof path.path === "string") {
        const parts = path.path.split(/[/\\]/);
        return parts[parts.length - 1] || "";
      }

      return "";
    }

    // 如果 path 是字符串，直接处理
    if (typeof path === "string") {
      // 处理 Windows 和 Unix 风格的路径
      const parts = path.split(/[/\\]/);
      return parts[parts.length - 1] || "";
    }

    return "";
  } catch (err) {
    console.error("获取文件名失败:", err);
    return "";
  }
};

// 方法 - 获取文件类型文本
const getFileTypeText = () => {
  return TypeLabels[formData.value.type] + "文件";
};

// 方法 - 选择文件
const isSelectingFile = ref(false);

const selectFile = async () => {
  // 如果已经在选择文件，直接返回
  if (isSelectingFile.value) {
    return;
  }

  try {
    isSelectingFile.value = true;
    // 获取当前选择的文件类型
    const currentType = formData.value.type;
    let fileType;

    // 根据当前具体的文件类型确定过滤器
    if (currentMainType.value === MainFileType.IMAGE) {
      fileType = "image";
    } else if (currentMainType.value === MainFileType.VIDEO) {
      fileType = "video";
    } else if (currentMainType.value === MainFileType.APP) {
      fileType = "app";
    } else {
      fileType = "other";
    }

    console.log("选择文件 - 当前类型:", currentType, "使用过滤器:", fileType);

    // 使用 Electron 的 API 选择文件
    const filePaths = await electronAPI.selectFiles({
      title: `选择${getFileTypeText()}`,
      fileType: fileType,
      properties: ["openFile"],
    });

    if (filePaths && filePaths.length > 0) {
      const filePath = filePaths[0];
      formData.value.filePath = filePath;

      // 每次选择新文件时都更新资源名称
      const fileName = getFileName(filePath);
      formData.value.showName = fileName.replace(/\.[^/.]+$/, ""); // 移除扩展名
    }
  } catch (err) {
    console.error("选择文件失败:", err);
    await alertService.alert({
      title: "选择文件失败",
      message: err.message || "未知错误",
      confirmButtonText: "确定",
    });
  } finally {
    isSelectingFile.value = false;
  }
};

// 方法 - 选择文件夹
const selectFolder = async () => {
  try {
    // 调用 Electron API 打开文件夹选择对话框
    const result = await electronAPI.selectFiles({
      properties: ["openDirectory"],
      title: "选择资源文件夹",
    });

    if (result && result.length > 0) {
      const dirPath = result[0].path;
      formData.value.filePath = dirPath;

      // 自动设置资源名称（如果尚未设置）
      if (!formData.value.showName) {
        const folderName = getFileName(dirPath);
        formData.value.showName = folderName;
      }

      console.log("选择的文件夹路径:", dirPath);
    }
  } catch (err) {
    console.error("选择文件夹失败:", err);
    await alertService.alert({
      title: "选择文件夹失败",
      message: err.message || "未知错误",
      confirmButtonText: "确定",
    });
  }
};

// 方法 - 移除文件
const removeFile = (event) => {
  if (event) event.stopPropagation();
  formData.value.filePath = "";
};

// 方法 - 选择封面
const selectCover = async () => {
  // 如果已经在选择文件，直接返回
  if (isSelectingFile.value) {
    return;
  }

  try {
    isSelectingFile.value = true;
    // 使用 Electron 的 API 选择文件，直接指定图片类型
    const filePaths = await electronAPI.selectFiles({
      title: "选择封面图片",
      fileType: "image",
      properties: ["openFile"],
    });

    if (filePaths && filePaths.length > 0) {
      const filePath = filePaths[0];
      formData.value.coverPath = filePath;
    }
  } catch (err) {
    console.error("选择封面失败:", err);
    await alertService.alert({
      title: "选择封面失败",
      message: err.message || "未知错误",
      confirmButtonText: "确定",
    });
  } finally {
    isSelectingFile.value = false;
  }
};

// 方法 - 移除封面
const removeCover = (event) => {
  if (event) event.stopPropagation();
  formData.value.coverPath = "";
};

// 方法 - 更改资源类型
const changeResourceType = (newType) => {
  console.log("更改资源类型 - 从", formData.value.type, "到", newType);

  // 如果类型没有变化，不做任何处理
  if (formData.value.type === newType) {
    console.log("资源类型没有变化，不做任何处理");
    return;
  }

  // 更新资源类型
  const oldType = formData.value.type;
  formData.value.type = newType;
  console.log("资源类型已更新 -", oldType, "->", newType);

  // 如果切换了类型，清空已选择的文件
  if (formData.value.filePath) {
    console.log("切换类型后清空已选择的文件");
    formData.value.filePath = "";

    // 如果是视频类型，也清空封面
    if (oldType === FileType.VIDEO && formData.value.coverPath) {
      console.log("清空视频封面");
      formData.value.coverPath = "";
    }
  }
};

// 方法 - 处理表单提交
const handleSubmit = async () => {
  // 验证表单
  if (!isEdit.value && !formData.value.filePath) {
    messageService.warning("请先上传相关资源");
    return;
  }
  if (!formData.value.showName) {
    messageService.warning("请输入资源名称");
    return;
  }

  if (!formData.value.category) {
    messageService.warning("请先选择分类");
    return;
  }

  // 创建更新后的资源对象
  const updatedResource = {
    ...props.resource, // 保留原始资源的所有属性
    showName: formData.value.showName,
    describe: formData.value.describe,
    groups: [formData.value.category],
    group: formData.value.category, // 同时添加 group 字段（单数形式），以兼容后端 API
    type: formData.value.type, // 确保使用具体的子类型值
  };

  console.log(
    "保存资源 - 类型:",
    formData.value.type,
    "主类型:",
    currentMainType.value
  );

  // 确保保留原始资源的关键字段
  if (isEdit.value) {
    console.log("编辑模式 - 确保保留原始资源的关键字段");
    // 确保保留 index 字段
    if (props.resource.index !== undefined) {
      updatedResource.index = props.resource.index;
    }
    // 确保保留 MD5 字段
    if (props.resource.MD5) {
      updatedResource.MD5 = props.resource.MD5;
    }
    // 确保保留 path 字段
    if (props.resource.path) {
      updatedResource.path = props.resource.path;
    }

    // 如果上传了新文件，更新文件路径
    if (formData.value.filePath) {
      updatedResource.filePath = formData.value.filePath;
      updatedResource.fileName = getFileName(formData.value.filePath);
    }

    // 如果上传了新封面，更新封面路径
    if (formData.value.coverPath) {
      updatedResource.coverPath = formData.value.coverPath;
    }
  } else {
    // 如果是添加模式，添加文件路径、封面路径和类型
    // 对于 Web 环境，filePath 可能是 File 对象或文件名
    // 对于 Electron 环境，filePath 应该是文件的完整路径
    // 确保 filePath 是字符串，而不是对象
    if (
      typeof formData.value.filePath === "object" &&
      formData.value.filePath !== null
    ) {
      if (formData.value.filePath.path) {
        updatedResource.filePath = formData.value.filePath.path;
        console.log(
          "使用对象的 path 属性作为 filePath:",
          formData.value.filePath.path
        );
      } else {
        // 如果没有 path 属性，尝试使用 name 属性
        updatedResource.filePath = formData.value.filePath.name || "";
        console.log(
          "使用对象的 name 属性作为 filePath:",
          formData.value.filePath.name
        );
      }
    } else {
      updatedResource.filePath = formData.value.filePath;
    }

    // 如果有封面，添加封面路径
    if (formData.value.coverPath) {
      // 确保 coverPath 是字符串，而不是对象
      if (
        typeof formData.value.coverPath === "object" &&
        formData.value.coverPath !== null
      ) {
        if (formData.value.coverPath.path) {
          updatedResource.coverPath = formData.value.coverPath.path;
          console.log(
            "使用对象的 path 属性作为 coverPath:",
            formData.value.coverPath.path
          );
        } else {
          // 如果没有 path 属性，尝试使用 name 属性
          updatedResource.coverPath = formData.value.coverPath.name || "";
          console.log(
            "使用对象的 name 属性作为 coverPath:",
            formData.value.coverPath.name
          );
        }
      } else {
        updatedResource.coverPath = formData.value.coverPath;
      }
    }

    // 添加文件名（从路径中提取）
    let fileName = "";
    if (formData.value.filePath) {
      fileName = getFileName(formData.value.filePath);
      console.log("提取的文件名:", fileName);
    }

    updatedResource.fileName = fileName;

    // 如果没有设置 showName，使用文件名（不带扩展名）
    if (!updatedResource.showName && fileName) {
      try {
        updatedResource.showName = fileName.replace(/\.[^/.]+$/, "");
      } catch (err) {
        console.error("设置 showName 失败:", err);
        updatedResource.showName = fileName;
      }
    }
  }

  // 触发保存事件
  emit("save", updatedResource);
};

// 生命周期钩子 - 组件挂载后
onMounted(() => {
  if (isEdit.value) {
    // 编辑模式：使用资源的实际分组
    if (props.resource.groups && props.resource.groups.length > 0) {
      formData.value.category = props.resource.groups[0];
    } else if (props.categories.length > 0) {
      // 如果资源没有分组，使用第一个可用分类
      formData.value.category = props.categories[0];
    }
  } else {
    // 添加模式：优先选中当前激活的分组（selectedCategory）
    if (
      props.selectedCategory &&
      props.selectedCategory !== "全部" &&
      props.categories.includes(props.selectedCategory)
    ) {
      // 如果有指定的选中分类，且该分类在可用分类列表中，则使用该分类
      formData.value.category = props.selectedCategory;
    } else if (props.categories.includes(DEFAULT_GROUPS[0])) {
      // 如果没有指定分类或指定的是"全部"，则选中默认分类
      formData.value.category = DEFAULT_GROUPS[0];
    } else if (props.categories.length > 0) {
      // 如果没有"默认"分类，使用第一个可用分类
      formData.value.category = props.categories[0];
    }
  }

  // 确保至少有一个分类被选中
  if (!formData.value.category && props.categories.length > 0) {
    formData.value.category = props.categories[0];
  }

  // 如果是编辑模式，初始化表单数据
  if (isEdit.value) {
    // 确保资源类型正确
    if (props.resource.type !== undefined) {
      formData.value.type = props.resource.type;
    }

    // 如果有封面路径，初始化封面路径
    if (props.resource.coverPath) {
      formData.value.coverPath = props.resource.coverPath;
    }

    // 如果有文件路径，初始化文件路径（通常编辑模式不需要这个）
    if (props.resource.filePath) {
      formData.value.filePath = props.resource.filePath;
    }
  }

  console.log("🌈 帮我看看现在 FormData 有啥数据：\n", props.resource);
});
</script>

<style scoped>
.resource-dialog {
  background-color: var(--color-card-background-online);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
  width: 40vw;
  overflow: hidden;
  padding: 20px 34px 34px;
}
.btn-close {
  width: 26px;
  display: flex;
  justify-content: flex-end;
  width: 100%;
  cursor: pointer;
}

.dialog-header {
  display: flex;
  justify-content: center;
  color: var(--color-menu-text);
  text-align: center;
  font-size: 24px;
  font-style: normal;
  font-weight: 480;
  line-height: 18px;
  margin-bottom: 34px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
}

form {
  padding: 0 var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-group {
  margin-bottom: 0;
}

.form-group.required label::after {
  content: " *";
  color: var(--color-danger);
}
#resource-name {
  border: none;
  padding: 4px 12px;
  height: 44px;
  background-color: var(--color-dialog-background);
  box-shadow: none;
}
#resource-description {
  border: none;
  padding: 4px 12px;
  height: auto;
  background-color: var(--color-dialog-background);
  box-shadow: none;
}

label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--color-text-primary);
}

input[type="text"],
textarea {
  width: 100%;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  color: var(--color-text-primary);
  background-color: var(--color-card-background);
  transition: border-color 0.2s;
  box-shadow: var(--shadow-sm);
}

input[type="text"]:focus,
textarea:focus {
  border-color: var(--color-primary);
  outline: none;
}

textarea {
  min-height: 100px;
  resize: vertical;
}

.tab-buttons {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
  border: 1px solid var(--color-border);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
}

.tab-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-card-background);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.tab-button:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* 暗色主题下的标签按钮悬停效果 */
[data-theme="dark"] .tab-button:hover {
  border-color: var(--color-primary-light);
  color: var(--color-primary-light);
}

.tab-button.active {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}

.file-upload-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: 4px 12px;
  height: 44px;
  background-color: var(--color-dialog-background);
  box-shadow: none;

  transition: all 0.2s;
}

.file-upload-box:hover {
  border-color: var(--color-primary);
  background-color: var(--color-primary-bg);
}

.file-upload-box.has-file {
  border-style: solid;
  border-color: var(--color-primary-light);
  background-color: var(--color-primary-bg);
}

.file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.file-name {
  font-size: 14px;
  color: var(--color-text-primary);
  word-break: break-all;
}

.remove-file-btn {
  background: none;
  border: none;
  color: var(--color-danger);
  font-size: 18px;
  cursor: pointer;
  margin-left: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  width: 30px;
  height: 30px;
  border-radius: 15px;
}

.remove-file-btn:hover {
  background-color: var(--color-danger-bg);
}

.upload-btn {
  margin-left: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.2s;
}

.upload-btn:hover {
  background-color: var(--color-primary-dark);
}

.button-group {
  display: flex;
  gap: var(--spacing-sm);
  margin-left: var(--spacing-sm);
}

.folder-btn {
  background-color: var(--color-secondary, #6c757d);
}

.folder-btn:hover {
  background-color: var(--color-secondary-dark, #5a6268);
}

.categories-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-dialog-background);

  max-height: 120px;
  overflow: hidden;
  overflow-y: auto;
  padding: 12px;
}

.category-radio {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.category-radio input[type="radio"] {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-md);
}

.cancel-btn,
.save-btn {
  padding: 8px 16px;
  width: 220px;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid var(--color-border);
}

.cancel-btn {
  background-color: var(--color-card-background);
  color: var(--color-text-primary);
}

.cancel-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* 暗色主题下的取消按钮 */
[data-theme="dark"] .cancel-btn:hover {
  border-color: var(--color-primary-light);
  color: var(--color-primary-light);
}

.save-btn {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: 2px solid var(--color-primary);
}

.save-btn:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

/* 暗色主题下的保存按钮 */
[data-theme="dark"] .save-btn {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: 2px solid var(--color-primary);
}

[data-theme="dark"] .save-btn:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.hidden-file-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.sub-type-select {
  width: 100%;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: 4px 12px;
  height: 44px;
  background-color: var(--color-dialog-background);
  box-shadow: none;
}

.sub-type-select:focus {
  border-color: var(--color-primary);
  outline: none;
}

/* 暗色主题下的下拉框 */
[data-theme="dark"] .sub-type-select {
  background-color: var(--color-background);
  color: var(--color-text-primary);
}

[data-theme="dark"] .sub-type-select:focus {
  border-color: var(--color-primary-light);
}
</style>
