<template>
  <div class="play-record">
    <div class="record-header">
      <div class="record-title">播放列表</div>
      <button class="btn-refresh" @click="refreshRecords" title="刷新">
        <span class="refresh-icon">↻</span>
      </button>
    </div>
    <div class="record-content">
      <div v-if="loading" class="loading-indicator">
        <div class="spinner"></div>
        <span>加载中...</span>
      </div>
      <div v-else-if="records.length === 0" class="empty-message">
        暂无发布记录
      </div>
      <div v-else class="record-list">
        <div
          v-for="record in records"
          :key="record.id"
          class="record-item"
          @click="handleRecordItemClick(record)"
        >
          <!-- 缩略图容器 -->
          <div class="record-thumbnail-container">
            <img
              v-if="getResourceThumbnail(record)"
              class="img-bg"
              :src="getResourceThumbnail(record)"
              :alt="getResourceName(record)"
              @error="handleImageError"
              crossorigin="anonymous"
            />
            <div v-else class="img-bg-placeholder" aria-label="无缩略图"></div>

            <!-- 资源信息覆盖层 -->
            <div class="record-resource-info">
              <h4>{{ getResourceName(record) }}</h4>
              <div class="tags">
                <div class="resource-tags">
                  <span class="tag-type">{{
                    getResourceTypeLabel(record)
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 记录项内容区域 -->
          <div class="record-item-content">
            <div class="record-item-header">
              <div class="record-info">
                <div class="record-name">{{ record.name || "未命名播控" }}</div>
                <div class="record-time">
                  {{ formatTime(record.createdAt) }}
                </div>
              </div>
              <div
                class="record-status-indicator"
                :class="getStatusClass(record.status)"
                :title="getStatusText(record.status)"
              ></div>
            </div>
            <div class="record-content-row">
              <div class="record-details">
                <div class="record-resources">
                  <div class="detail-value">
                    资源:
                    {{ getResourceNames(record.resources) }}
                  </div>
                </div>
                <div class="record-devices">
                  <div class="detail-value">
                    设备:{{ record.devices.length }}台
                  </div>
                </div>
              </div>
              <div class="record-actions">
                <div
                  v-if="
                    record.status === 'stopped' || record.status === 'active'
                  "
                  class="btn-play"
                  @click.stop="editRecord(record)"
                >
                  <img src="@assets/svg/control/play_edit.svg" alt="" />
                </div>
                <div class="v-line"></div>
                <!-- v-if="record.status === 'active'" -->
                <div
                  class="btn-play"
                  @click.stop="
                    stopRecord(
                      record,
                      getResourceTypeByIndex(record.resources[0]),
                      getResourcePkgByIndex(record.resources[0])
                    )
                  "
                >
                  <img src="@assets/svg/control/play_cancel.svg" alt="" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useElectronAPI } from "../plugins/electron";
import alertService from "../plugins/alert";
import messageService from "../plugins/message";

// 使用Electron API
const electronAPI = useElectronAPI();

// Props
const props = defineProps({
  // 可以传入设备列表和资源列表，用于显示名称
  devices: {
    type: Array,
    default: () => [],
  },
  resources: {
    type: Array,
    default: () => [],
  },
  // 是否启用自动刷新
  autoRefresh: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits([
  "edit",
  "stop",
  "refresh",
  "delete",
  "republish",
  "show-play-view",
]);
// 状态
const records = ref([]);
const loading = ref(false);
const refreshInterval = ref(null);

// 方法 - 获取发布记录
const fetchRecords = async () => {
  try {
    loading.value = true;

    // 调用API获取发布记录
    const fetchedRecords = await electronAPI.getPublishRecords();

    // 更新记录
    records.value = fetchedRecords;

    console.log("播放列表数据：", fetchedRecords);
  } catch (error) {
    console.error("获取发布记录失败:", error);
    alertService.alert({
      title: "获取失败",
      message: `获取发布记录失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  } finally {
    loading.value = false;
  }
};

// 方法 - 刷新记录
const refreshRecords = () => {
  fetchRecords();

  // 触发刷新事件，通知父组件
  emit("refresh");
};

// 方法 - 编辑记录
const editRecord = async (record) => {
  // 移除状态检查，允许编辑发布中的记录
  // 触发编辑事件
  emit("edit", record);
};

// 方法 - 处理记录项点击
const handleRecordItemClick = (record) => {
  // 检查记录是否有资源
  if (!record.resources || record.resources.length === 0) {
    console.warn("记录没有关联的资源:", record);
    return;
  }

  // 获取第一个资源的index
  const firstResourceIndex = record.resources[0];

  // 从props.resources中找到对应的资源对象
  const resource = props.resources.find((r) => r.index === firstResourceIndex);

  if (!resource) {
    console.warn("未找到对应的资源对象，index:", firstResourceIndex);
    return;
  }

  // 模拟ResourceCard中的handleCardClick逻辑
  // 如果资源已发布，显示播放视图
  if (resource.isPublished || record.status === "active") {
    console.log("🌈显示播放画面 (从记录点击):", resource);

    // 设置创建时间并触发show-play-view事件
    resource.createdAt = new Date().toISOString();
    emit("show-play-view", resource, 0); // index设为0，因为这是从记录点击的
  } else {
    console.log("资源未发布，无法显示播放画面");
  }
};

// 方法 - 再次发布记录
const republishRecord = async (record) => {
  if (record.status === "active") {
    return;
  }

  try {
    // 确认对话框
    const confirmed = await alertService.confirm({
      title: "确认再次发布",
      message: `确定要再次发布此记录吗？这将向所有相关设备发送发布命令。`,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    if (!confirmed) {
      return;
    }

    loading.value = true;

    // 获取设备列表
    const deviceSNs = Array.isArray(record.devices)
      ? record.devices.map((sn) => String(sn))
      : [];

    if (deviceSNs.length === 0) {
      alertService.alert({
        title: "发布失败",
        message: "没有可用的设备",
        confirmButtonText: "确定",
      });
      return;
    }

    // 获取资源列表
    const resourceIndexes = Array.isArray(record.resources)
      ? record.resources
      : [];

    if (resourceIndexes.length === 0) {
      alertService.alert({
        title: "发布失败",
        message: "没有可用的资源",
        confirmButtonText: "确定",
      });
      return;
    }

    // 发布资源到设备
    for (const resourceIndex of resourceIndexes) {
      await electronAPI.publishResource(
        resourceIndex,
        deviceSNs,
        record.loopPlay || false
      );
    }

    // 更新记录状态为活动
    const updatedRecord = await electronAPI.activatePublishRecord(record.id);

    // 触发发布记录更新事件，通知组件刷新
    window.dispatchEvent(new CustomEvent("publish-record-updated"));

    // 刷新记录列表
    await fetchRecords();

    // 触发再次发布事件
    emit("republish", updatedRecord);

    // 显示成功提示
    alertService.alert({
      title: "发布成功",
      message: "已成功再次发布",
      confirmButtonText: "确定",
    });
  } catch (error) {
    console.error("再次发布失败:", error);
    alertService.alert({
      title: "发布失败",
      message: `再次发布失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  } finally {
    loading.value = false;
  }
};
// 方法 - 删除记录
const deleteRecord = async (record) => {
  try {
    // 如果记录处于活动状态，提示用户先停止发布
    if (record.status === "active") {
      await alertService.alert({
        title: "无法删除",
        message: "发布中的记录不能直接删除，请先停止发布。",
        confirmButtonText: "确定",
      });
      return;
    }

    // 确认对话框
    const confirmed = await alertService.confirm({
      title: "确认删除",
      message: `确定要删除此发布记录吗？此操作不可撤销。`,
      confirmButtonText: "确定删除",
      cancelButtonText: "取消",
    });

    if (!confirmed) {
      return;
    }

    loading.value = true;

    // 删除记录
    await electronAPI.deletePublishRecord(record.id);

    // 触发发布记录更新事件，通知组件刷新
    window.dispatchEvent(new CustomEvent("publish-record-updated"));

    // 刷新记录列表
    await fetchRecords();

    // 触发删除事件
    emit("delete", record);

    // 显示成功提示
    alertService.alert({
      title: "删除成功",
      message: "已成功删除发布记录",
      confirmButtonText: "确定",
    });
  } catch (error) {
    console.error("删除发布记录失败:", error);
    alertService.alert({
      title: "删除失败",
      message: `删除发布记录失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  } finally {
    loading.value = false;
  }
};

// 方法 - 停止记录
const stopRecord = async (record, resourceType, packageName) => {
  if (record.status === "stopped") {
    return;
  }

  try {
    // 确认对话框
    const confirmed = await alertService.confirm({
      title: "确认停止",
      message: `确定要停止此发布记录吗？这将停止所有相关设备的发布。`,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    if (!confirmed) {
      return;
    }

    loading.value = true;

    // 首先停止设备的发布
    // 确保传递的是简单的字符串数组
    const deviceSNs = Array.isArray(record.devices)
      ? record.devices.map((sn) => String(sn))
      : [];

    // 注意：不在这里发送停止命令，而是由ControlPage.vue中的stopPlayRecord方法发送
    // 这样可以避免发送两次停止命令

    // 然后更新记录状态
    const updatedRecord = await electronAPI.stopPublishRecord(record.id);

    // 触发发布记录更新事件，通知组件刷新
    window.dispatchEvent(new CustomEvent("publish-record-updated"));

    // 刷新记录列表
    await fetchRecords();

    // 触发停止事件
    emit("stop", updatedRecord, resourceType, packageName);

    messageService.success("已成功停止发布");
  } catch (error) {
    console.error("停止发布失败:", error);
    alertService.alert({
      title: "停止失败",
      message: `停止发布失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  } finally {
    loading.value = false;
  }
};

// 辅助方法 - 格式化时间
const formatTime = (timeString) => {
  const date = new Date(timeString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

// 辅助方法 - 获取状态类名
const getStatusClass = (status) => {
  switch (status) {
    case "active":
      return "status-active";
    case "stopped":
      return "status-stopped";
    default:
      return "";
  }
};

// 辅助方法 - 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case "active":
      return "发布中";
    case "stopped":
      return "已停止";
    default:
      return "未知状态";
  }
};

// 辅助方法 - 获取资源名称
const getResourceNames = (resourceIndexes) => {
  if (!resourceIndexes || resourceIndexes.length === 0) return "无";

  // 如果资源数量超过1个，只显示第一个资源名称和总数
  if (resourceIndexes.length > 1) {
    const firstResource = props.resources.find(
      (r) => r.index === resourceIndexes[0]
    );
    const firstResourceName = firstResource
      ? firstResource.showName ||
        firstResource.fileName ||
        `资源 ${resourceIndexes[0]}`
      : `资源 ${resourceIndexes[0]}`;
    return `${firstResourceName} 等${resourceIndexes.length}个资源`;
  }

  // 只有一个资源时，显示完整名称
  const resource = props.resources.find((r) => r.index === resourceIndexes[0]);
  return resource
    ? resource.showName || resource.fileName || `资源 ${resourceIndexes[0]}`
    : `资源 ${resourceIndexes[0]}`;
};

const getResourceTypeByIndex = (index) => {
  const res = props.resources.find((r) => r.index === index);
  return res ? res.type : undefined;
};

const getResourcePkgByIndex = (index) => {
  const res = props.resources.find((r) => r.index === index);
  return res ? res.pkg || res.packageName : undefined;
};

// 辅助方法 - 获取记录的资源名称
const getResourceName = (record) => {
  if (!record.resources || record.resources.length === 0) return "无资源";

  const firstResourceIndex = record.resources[0];
  const resource = props.resources.find((r) => r.index === firstResourceIndex);

  return resource
    ? resource.showName || resource.fileName || `资源 ${firstResourceIndex}`
    : `资源 ${firstResourceIndex}`;
};

// 辅助方法 - 获取记录的资源类型标签
const getResourceTypeLabel = (record) => {
  if (!record.resources || record.resources.length === 0) return "未知";

  const firstResourceIndex = record.resources[0];
  const resource = props.resources.find((r) => r.index === firstResourceIndex);

  if (!resource) return "未知";

  // 根据资源类型返回标签
  if (resource.type === 15) return "应用";
  if (resource.type < 4) return "图片";
  return "视频";
};

// 辅助方法 - 获取记录的资源缩略图
const getResourceThumbnail = (record) => {
  if (!record.resources || record.resources.length === 0) return null;

  const firstResourceIndex = record.resources[0];
  const resource = props.resources.find((r) => r.index === firstResourceIndex);

  console.log("🌈 资源为 resource", resource);

  if (!resource) return null;

  // 这里可以根据需要实现缩略图获取逻辑
  // 暂时返回 null，让组件显示占位符
  return null;
};

// 辅助方法 - 处理图片加载错误
const handleImageError = (event) => {
  // 使用默认占位符
  event.target.style.display = "none";
};

// 辅助方法 - 获取设备名称
const getDeviceNames = (deviceSNs) => {
  if (!deviceSNs || deviceSNs.length === 0) return "无";

  return deviceSNs
    .map((sn) => {
      const device = props.devices.find((d) => d.sn === sn);
      return device ? device.id || device.sn : sn;
    })
    .join(", ");
};

// 生命周期钩子
onMounted(() => {
  // 立即获取记录
  fetchRecords();

  // 只有在启用自动刷新时才设置定时器
  if (props.autoRefresh) {
    refreshInterval.value = setInterval(() => {
      fetchRecords();
    }, 10000); // 每10秒刷新一次
  }

  // 监听发布记录更新事件
  const handleRecordUpdate = () => {
    fetchRecords();
  };

  window.addEventListener("publish-record-updated", handleRecordUpdate);

  // 保存事件处理函数的引用，以便在卸载时移除
  window._recordUpdateHandler = handleRecordUpdate;
});

onUnmounted(() => {
  // 清除定时刷新
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }

  // 移除事件监听
  if (window._recordUpdateHandler) {
    window.removeEventListener(
      "publish-record-updated",
      window._recordUpdateHandler
    );
    delete window._recordUpdateHandler;
  }
});
</script>

<style scoped>
.play-record {
  background-color: var(--color-card-background);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  height: 40px;
  box-sizing: border-box;

  color: #595b6a;
  text-align: center;
  font-size: 23.717px;
  font-style: normal;
  font-weight: 480;
  line-height: 17.788px; /* 75% */

  margin-bottom: 24px;
}

.btn-refresh {
  background: none;
  border: none;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.btn-refresh:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.refresh-icon {
  font-size: 20px;
  color: var(--color-text-secondary, #999);
}

.record-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xs);
  max-height: calc(100% - 40px);
  align-items: flex-start;
  padding-top: 0;
}

.record-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  width: 100%;
  align-self: flex-start;
}

.record-item {
  background-color: #d9e8f8;
  border-radius: var(--border-radius-sm);
  padding: 0;
  margin-bottom: var(--spacing-xs);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

.record-item:hover {
  background-color: #c5ddf5;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 缩略图容器 */
.record-thumbnail-container {
  position: relative;
  width: 120px;
  height: 80px;
  flex-shrink: 0;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 缩略图图片 */
.record-thumbnail-container .img-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.record-item:hover .record-thumbnail-container .img-bg {
  transform: scale(1.05);
}

/* 缩略图占位符 */
.record-thumbnail-container .img-bg-placeholder {
  width: 100%;
  height: 100%;
  background: #d3e7fe;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 12px;
}

.record-thumbnail-container .img-bg-placeholder::before {
  content: "无缩略图";
}

/* 资源信息覆盖层 */
.record-resource-info {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 8px;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.6),
    rgba(0, 0, 0, 0.3),
    transparent
  );
  color: var(--color-white);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.record-resource-info h4 {
  margin: 0 0 4px 0;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #fff;
}

/* 标签样式 */
.record-resource-info .tags {
  display: inline-flex;
  gap: 4px;
}

.record-resource-info .resource-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.record-resource-info .tag-type {
  display: inline-block;
  padding: 1px 4px;
  background-color: #f69422;
  color: var(--color-white);
  font-size: 10px;
  border-radius: 3px;
}

/* 记录项内容区域 */
.record-item-content {
  flex: 1;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
}

.record-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.record-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.record-name {
  font-weight: 500;
  color: var(--color-text-primary);
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.record-time {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.record-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
}

.status-active {
  background-color: var(--color-success);
  box-shadow: 0 0 4px var(--color-success);
  animation: pulse-green 2s infinite;
}

.status-stopped {
  background-color: var(--color-danger);
  box-shadow: 0 0 3px var(--color-danger);
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

.record-content-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-details {
  flex: 1;
}

.record-resources,
.record-devices {
  font-size: 12px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.detail-label {
  color: var(--color-text-secondary);
  margin-right: var(--spacing-xs);
  font-size: 11px;
}

.detail-value {
  color: var(--color-text-primary);
  font-size: 12px;
  width: 150px;
  white-space: nowrap; /* 禁止换行 */
  text-overflow: ellipsis; /* 超出内容用省略号表示 */
  overflow: hidden;
}

.record-actions {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: var(--spacing-sm);
}
.v-line {
  width: 1px;
  height: 15px;
  margin: 0 5px;
  background-color: #989898;
}
.btn-play {
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.btn-xs {
  padding: 2px 6px;
  font-size: 11px;
  height: 22px;
  width: 100%;
  min-width: 50px;
}

.loading-indicator,
.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: var(--spacing-md);
  min-height: 100px;
  color: var(--color-text-secondary);
  width: 100%;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-sm);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
