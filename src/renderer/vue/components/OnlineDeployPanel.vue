<template>
  <div class="online-deploy-panel">
    <!-- 顶部栏 -->
    <div class="header">
      <el-button
        type="primary"
        class="batch-btn"
        @click="emitBatchDeploy"
        :disabled="!multipleSelection.length"
        >批量部署</el-button
      >
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-select
        v-model="deviceStatus"
        placeholder="全部"
        size="small"
        style="width: 100px"
      >
        <el-option label="全部" value="all" />
        <el-option label="在线" value="online" />
        <el-option label="离线" value="offline" />
      </el-select>
      <span class="device-count"
        >发现设备数量：{{ filteredDevices.length }}</span
      >
      <el-input
        v-model="search"
        placeholder="输入SN号搜索设备"
        size="small"
        class="search-input"
        prefix-icon="el-icon-search"
        style="width: 200px"
      />
    </div>

    <!-- 设备表格 -->
    <el-table
      :data="filteredDevices"
      style="width: 100%"
      border
      :fit="true"
      :max-height="tableHeight"
      @selection-change="handleSelectionChange"
      :row-class-name="rowClassName"
    >
      <el-table-column
        type="selection"
        width="55"
        :selectable="
          (row) =>
            row.status === '在线' &&
            row.deployStatus !== '部署中' &&
            row.deployStatus !== '部署成功'
        "
      />
      <el-table-column prop="deviceNo" label="设备号" width="120" />
      <el-table-column prop="sn" label="设备SN号" width="180" />
      <el-table-column prop="model" label="设备型号" width="100">
        <template #default="scope">
          {{ scope.row.sn ? scope.row.sn.substring(0, 2) : "" }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="设备状态" width="100">
        <template #default="scope">
          <span
            :style="{
              color: scope.row.status === '在线' ? '#67C23A' : '#909399',
            }"
            >{{ scope.row.status }}</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="deployStatus" label="部署状态">
        <template #default="scope">
          <template v-if="scope.row.deployStatus === '部署中'">
            <div style="display: flex; align-items: center; gap: 8px">
              <el-progress
                :percentage="scope.row.deployProgress || 0"
                :show-text="false"
                style="width: 100px"
              />
              <span
                v-if="scope.row.deployStatus === '部署中'"
                style="font-size: 12px; color: #888"
              >
                {{ scope.row.deployProgress || 0 }}%
              </span>
            </div>
          </template>
          <template v-else>
            <span :style="deployStatusColor(scope.row.deployStatus)">{{
              scope.row.deployStatus
            }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="deployTime" label="部署时间" />
      <el-table-column label="更多操作">
        <template #default="scope">
          <div style="display: flex; gap: 8px">
            <el-button
              v-if="scope.row.status === '在线'"
              :type="scope.row.deployStatus === '部署中' ? 'danger' : 'primary'"
              size="small"
              :disabled="false"
              @click="
                scope.row.deployStatus === '部署中'
                  ? emitCancelDeploy(scope.row)
                  : emitDeploy(scope.row)
              "
              >{{
                scope.row.deployStatus === "部署中" ? "取消部署" : "部署"
              }}</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useDeployStore } from "../stores/deploy";
import eventService from "../../services/EventService";
import messageService from "../plugins/message";

const props = defineProps({
  devices: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(["deploy", "cancel-deploy", "batch-deploy"]);

const deployStore = useDeployStore();
const deviceStatus = ref("all");
const search = ref("");
const multipleSelection = ref([]);
const MAX_CONCURRENT_DEPLOYS = 5; // 添加最大并发部署数量限制

// 添加配置传输定时器Map
const configTransferTimers = new Map();

// 清除配置传输定时器
const clearConfigTimer = (deviceSN) => {
  if (configTransferTimers.has(deviceSN)) {
    clearTimeout(configTransferTimers.get(deviceSN));
    configTransferTimers.delete(deviceSN);
  }
};

// 添加表格高度计算
const tableHeight = ref(0);

// 计算表格高度
const calculateTableHeight = () => {
  // 减去头部和工具栏的高度，以及一些边距
  tableHeight.value = window.innerHeight - 200;
};

// 监听窗口大小变化
onMounted(() => {
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);

  // 监听设备状态变化
  eventService.on("device:disconnected", (ip) => {
    console.log("设备断开连接:", ip);
    // 查找对应的设备
    const device = props.devices.find((d) => d.ip === ip);
    if (device) {
      console.log("找到断开连接的设备:", device.sn);
      // 更新设备状态为离线
      device.status = "离线";
      device.isOnline = false;
      // 如果设备正在部署中，更新状态为失败
      if (device.deployStatus === "部署中") {
        device.deployStatus = "失败";
        device.deployProgress = 0;
        // 减少部署计数
        deployStore.cancelDeploy(device.sn);
        messageService.error(`设备 ${device.sn} 离线，部署已中断`);
      }
    }
  });

  // 监听设备状态更新事件
  eventService.on("device:statusUpdated", ({ ip, status }) => {
    if (!status.sn) return;
    //console.log('设备状态更新:', status.sn, status.deviceStatus);
    const device = props.devices.find((d) => d.sn === status.sn);
    if (device) {
      device.isOnline = status.deviceStatus === 1;
      device.status = device.isOnline ? "在线" : "离线";
      // 更新设备IP
      device.ip = ip;
    }
  });

  // 监听设备连接事件
  eventService.on("device:connected", ({ ip, status }) => {
    if (!status.sn) return;
    console.log("设备连接:", status.sn);
    const device = props.devices.find((d) => d.sn === status.sn);
    if (device) {
      device.isOnline = true;
      device.status = "在线";
      device.ip = ip;
    }
  });

  // 监听部署错误事件
  eventService.on("deploy:error", (data) => {
    const device = props.devices.find((d) => d.sn === data.deviceSN);
    if (device) {
      device.deployStatus = "失败";
      device.deployProgress = 0;
      // 减少部署计数
      deployStore.cancelDeploy(device.sn);
      messageService.error(`设备 ${device.sn} 部署失败: ${data.message}`);
    }
  });
});

onUnmounted(() => {
  window.removeEventListener("resize", calculateTableHeight);
  eventService.getEventBus().removeAllListeners("device:disconnected");
  eventService.getEventBus().removeAllListeners("deploy:error");
  eventService.getEventBus().removeAllListeners("deploy:success");
});

const filteredDevices = computed(() => {
  let list = props.devices;
  if (deviceStatus.value !== "all") {
    list = list.filter((d) =>
      deviceStatus.value === "online"
        ? d.status === "在线"
        : d.status === "离线"
    );
  }
  if (search.value) {
    const searchText = search.value.toLowerCase();
    list = list.filter((d) => d.sn && d.sn.toLowerCase().includes(searchText));
  }
  // 按 deviceNo 排序
  return list.sort((a, b) => {
    // 如果 deviceNo 是数字，按数字大小排序
    const aNo = parseInt(a.deviceNo);
    const bNo = parseInt(b.deviceNo);
    if (!isNaN(aNo) && !isNaN(bNo)) {
      return aNo - bNo;
    }
    // 如果 deviceNo 不是数字，按字符串排序
    return (a.deviceNo || "").localeCompare(b.deviceNo || "");
  });
});

function handleSelectionChange(val) {
  multipleSelection.value = val;
}

function emitDeploy(row) {
  // 检查设备是否在线
  if (row.status !== "在线") {
    messageService.warning(`设备 ${row.sn} 不在线，无法部署`);
    return;
  }

  // 检查当前部署中的设备数量
  if (deployStore.deployingDevices.size >= MAX_CONCURRENT_DEPLOYS) {
    messageService.warning(
      `当前已有 ${MAX_CONCURRENT_DEPLOYS} 台设备正在部署，请等待部分设备部署完成后再试`
    );
    return;
  }

  deployStore.startDeploy(row.sn);
  row.deployStatus = "部署中";
  row.deployProgress = 0;
  emit("deploy", row);
}

function emitCancelDeploy(row) {
  try {
    console.log("取消部署设备:", row.sn);
    // 先调用后端的取消部署
    emit("cancel-deploy", row);
    // 然后更新前端状态
    deployStore.cancelDeploy(row.sn);
    // 立即更新设备状态为失败
    row.deployStatus = "失败";
    row.deployProgress = 0;
    row.deployFileIndex = undefined;
    row.deployFileTotal = undefined;
    // 清除该设备的定时器
    clearConfigTimer(row.sn);
  } catch (error) {
    console.error("取消部署失败:", error);
    messageService.error("取消部署失败: " + (error.message || "未知错误"));
  }
}

function emitBatchDeploy() {
  // 过滤出在线的且未部署成功的设备
  const onlineDevices = multipleSelection.value.filter(
    (device) =>
      device.status === "在线" &&
      device.deployStatus !== "部署成功" &&
      device.deployStatus !== "部署中"
  );

  if (onlineDevices.length === 0) {
    messageService.warning("没有可部署的设备");
    return;
  }

  if (onlineDevices.length < multipleSelection.value.length) {
    messageService.warning(
      `有 ${multipleSelection.value.length - onlineDevices.length} 个设备不可部署（已部署成功或不在线），将只部署可用设备`
    );
  }

  // 检查当前部署中的设备数量
  const availableSlots =
    MAX_CONCURRENT_DEPLOYS - deployStore.deployingDevices.size;
  if (availableSlots <= 0) {
    messageService.warning(
      `当前已有 ${MAX_CONCURRENT_DEPLOYS} 台设备正在部署，请等待部分设备部署完成后再试`
    );
    return;
  }

  // 只部署可用的设备数量
  const devicesToDeploy = onlineDevices.slice(0, availableSlots);
  if (devicesToDeploy.length < onlineDevices.length) {
    messageService.warning(
      `由于并发限制，只能同时部署 ${availableSlots} 台设备，其余设备将在当前部署完成后继续`
    );
  }

  devicesToDeploy.forEach((device) => {
    deployStore.startDeploy(device.sn);
    device.deployStatus = "部署中";
    device.deployProgress = 0;
  });
  emit("batch-deploy", devicesToDeploy);
}

function rowClassName({ row }) {
  if (row.deployStatus === "部署成功") return "row-deployed";
  if (row.status === "离线") return "row-offline";
  if (row.deployStatus === "失败") return "row-failed";
  return "";
}

function deployStatusColor(status) {
  if (status === "部署成功") return { color: "#67C23A" };
  if (status === "未部署") return { color: "#409EFF" };
  if (status === "失败") return { color: "#F56C6C" };
  return { color: "#F56C6C" };
}
</script>

<style scoped>
.online-deploy-panel {
  background: #fff;
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

/* 添加表格容器样式 */
.el-table {
  flex: 1;
  height: calc(100vh - 200px); /* 减去头部和工具栏的高度 */
  overflow: hidden;
}

/* 自定义滚动条样式 */
.el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background: var(--color-background-light);
  border-radius: 4px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

.batch-btn {
  margin-left: auto;
}
.device-count {
  color: #909399;
}
.search-input {
  margin-left: auto;
}
.el-table .row-deployed {
  background: #f0f9eb;
}
.el-table .row-offline {
  color: #bfbfbf;
}
.el-table .row-failed {
  color: #f56c6c;
}
</style>
