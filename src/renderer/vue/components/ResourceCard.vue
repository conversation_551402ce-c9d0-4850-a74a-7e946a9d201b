<template>
  <div
    class="resource-card"
    :class="[mode + '-mode']"
    :data-id="resource.index || resource.id || index"
    :data-type="resource.type"
    @click="handleCardClick"
    @contextmenu.prevent="showContextMenu"
  >
    <!-- 缩略图 -->
    <div class="thumbnail-container">
      <img
        v-if="thumbnailUrl"
        class="img-bg"
        :src="thumbnailUrl"
        :alt="resource.showName || resource.fileName || '资源'"
        @error="handleImageError"
        crossorigin="anonymous"
      />
      <div v-else class="img-bg-placeholder" aria-label="无缩略图"></div>
      <!-- 预览图标 -->
      <!-- <div
        v-if="showPreview"
        class="preview-overlay"
        @click.stop="handlePreviewClick"
      >
        <img
          :src="isVideo ? playIcon : previewIcon"
          class="preview-icon"
          alt="预览"
        />
      </div> -->
      <div class="resource-info">
        <h4>{{ resource.showName || resource.fileName || "未命名资源" }}</h4>
        <div class="tags">
          <div class="resource-tags">
            <span class="tag-type">{{
              resource.type == 15 ? "应用" : resource.type < 4 ? "图片" : "视频"
            }}</span>
          </div>
          <div class="resource-tags">
            <span class="resource-type">{{ typeLabel }}</span>
            <span
              v-if="resource.categories && resource.categories.length > 0"
              class="resource-category"
              >{{ resource.categories[0] }}</span
            >
          </div>
        </div>
        <!-- 显示 APK 包名信息 -->
        <div v-if="isApp && resource.pkg" class="apk-info">
          <span class="package-name">{{ resource.pkg }}</span>
        </div>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div v-if="showControls">
      <div class="card-texts" v-if="!enableContextMenu">
        <div class="online-status">
          <span
            class="status-indicator"
            :class="resource.isPublished ? 'online' : 'offline'"
          ></span>
          <span>{{ resource.isPublished ? "已发布" : "未发布" }}</span>
        </div>

        <!-- <div v-if="resource.isPublished">
          发布时间：{{ formatTime(resource.createdAt) ?? "" }}
        </div> -->
      </div>
      <div class="card-controls">
        <button
          v-if="controls.includes('publish') && !resource.isPublished"
          class="publish-btn"
          title="发布"
          @click.stop="handlePublish"
        >
          <img src="@assets/svg/control/publish.svg" alt="" />
          发布
        </button>
        <template v-if="resource.isPublished">
          <div
            class="edit-btn"
            title="编辑发布"
            @click.stop="handleEditPublish"
          >
            <img src="@assets/svg/public_edit.svg" alt="" />
          </div>
          <div
            class="stop-btn"
            title="停止发布"
            @click.stop="handleStopPublish"
          >
            <img src="@assets/svg/public_cancel.svg" alt="" />
          </div>
        </template>
      </div>
    </div>

    <!-- 右键菜单 -->
    <Teleport to="body">
      <div v-if="showMenu" class="context-menu" :style="menuStyle">
        <div class="menu-item" @click="handleEdit">编辑</div>
        <div class="menu-item" @click="handleDelete">删除</div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from "vue";
import {
  TypeLabels,
  FileType,
  FileTypeRanges,
  isVideoType,
  isAppType,
} from "../../../shared/types/resource.js";
import { useElectronAPI } from "../plugins/electron";
import defaultAppCover from "../../assets/images/yvr_defualt_app.png";
import defaultPoster from "../../assets/images/yvr_defualtposter.png";
import playIcon from "../../assets/images/yvr_play_tab.png";
import previewIcon from "../../assets/images/yvr_recenter.png";
import alertService from "../plugins/alert";

const electronAPI = useElectronAPI();

// 定义props
const props = defineProps({
  resource: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    default: 0,
  },
  mode: {
    type: String,
    default: "view",
    validator: (value) => ["view", "edit"].includes(value),
  },
  showControls: {
    type: Boolean,
    default: true,
  },
  controls: {
    type: Array,
    default: () => ["edit", "delete", "publish"],
  },
  publishRecords: {
    type: Array,
    default: () => [],
  },
  enableContextMenu: {
    type: Boolean,
    default: false,
  },
  // 添加新的prop控制是否显示预览功能
  showPreview: {
    type: Boolean,
    default: false,
  },
});

// 定义事件
const emit = defineEmits([
  "click",
  "edit",
  "delete",
  "publish",
  "play-video",
  "preview-image",
  "edit-publish",
  "stop-publish",
  "show-play-view",
]);

// 缩略图URL
const thumbnailUrl = ref("");

// 默认缩略图路径 - 使用导入的图片
const DEFAULT_THUMBNAIL = defaultPoster;
// 标记是否已经使用了默认图片，防止无限循环
const isUsingDefaultImage = ref(false);

// 计算属性 - 资源类型标签
const typeLabel = computed(() => {
  // 使用 TypeLabels 获取类型标签
  const label = TypeLabels[props.resource.type];
  return label || "未知类型";
});

// 计算属性 - 是否为视频类型
const isVideo = computed(() => {
  return isVideoType(props.resource.type);
});

// 计算属性 - 是否为应用类型
const isApp = computed(() => {
  return isAppType(props.resource.type);
});

// 计算属性 - 获取当前资源的发布记录
const currentPublishRecord = computed(() => {
  return props.publishRecords.find((record) =>
    record.resources.includes(props.resource.index)
  );
});

// 处理图片加载错误
const handleImageError = (event) => {
  // 如果已经在使用默认图片，不再处理错误
  if (isUsingDefaultImage.value) {
    // 防止继续触发错误事件
    event.target.removeEventListener("error", handleImageError);
    return;
  }

  // 标记为已使用默认图片
  isUsingDefaultImage.value = true;

  // 只在开发模式下输出日志
  if (process.env.NODE_ENV === "development") {
    console.log("ResourceCard: 图片加载失败，使用默认图片");
  }

  // 使用默认图片
  event.target.src =
    props.resource.type == 15 ? defaultAppCover : DEFAULT_THUMBNAIL;
};

// 处理卡片点击 - 现在卡片点击不再触发任何操作
const handleCardClick = (event) => {
  // 检查点击是否来自复选框或其父元素
  const isCheckboxClick = event.target.closest(".select-checkbox");

  if (isCheckboxClick) {
    // 如果是复选框点击，不做任何处理，让事件继续冒泡
    console.log("复选框点击，允许事件继续传播");
    return;
  }

  if (props.resource.isPublished) {
    showPlayView();
    return;
  }

  // 卡片点击不再触发任何操作，只有右上角的按钮才能触发选中、编辑和删除功能
  console.log("卡片点击，但不触发任何操作");
  // 阻止事件冒泡，确保不会触发其他事件
  event.stopPropagation();
};

// 处理预览/播放图标点击
const handlePreviewClick = () => {
  if (isVideo.value) {
    // 视频资源，触发播放事件
    console.log("点击了视频播放图标，触发播放事件:", props.resource);
    emit("play-video", props.resource, props.index);
  } else {
    // 图片资源，触发预览事件
    console.log("点击了图片预览图标，触发预览事件:", props.resource);
    // 使用专门的预览图片事件，而不是通用的click事件
    emit("preview-image", props.resource, props.index);
  }
};

// 处理编辑按钮点击
const handleEdit = () => {
  emit("edit", props.resource, props.index);
};

// 处理删除按钮点击
const handleDelete = () => {
  emit("delete", props.resource, props.index);
};

// 处理发布按钮点击
const handlePublish = () => {
  (props.resource.createdAt = new Date().toISOString()),
    emit("publish", props.resource, props.index);
};

// 处理发布按钮点击
const showPlayView = () => {
  console.log("🌈显示播放画面:", props.resource);

  (props.resource.createdAt = new Date().toISOString()),
    emit("show-play-view", props.resource, props.index);
};

// 处理编辑按钮点击
const handleEditPublish = () => {
  console.log("编辑发布:", props.resource);
  if (currentPublishRecord.value) {
    emit("edit-publish", currentPublishRecord.value);
  } else {
    console.error("未找到发布记录");
  }
};

// 处理停止按钮点击
const handleStopPublish = async () => {
  // 确认对话框
  const confirmed = await alertService.confirm({
    title: "确认停止",
    message: `确定要停止此发布记录吗？这将停止所有相关设备的发布。`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (!confirmed) {
    return;
  }

  console.log("停止发布:", props.resource);
  if (currentPublishRecord.value) {
    emit(
      "stop-publish",
      currentPublishRecord.value,
      props.resource.type,
      props.resource.pkg
    );
  } else {
    console.error("未找到发布记录");
  }
};

// 获取资源文件路径
const getResourcePath = async (filePath) => {
  try {
    const path = await electronAPI.getResourceFilePath(filePath);
    return path;
  } catch (error) {
    if (process.env.NODE_ENV === "development") {
      console.error("ResourceCard: 获取资源路径失败", error);
    }
    return null;
  }
};

// 拼接完整路径
const joinPath = (basePath, fileName) => {
  return basePath.endsWith("/")
    ? `${basePath}${fileName}`
    : `${basePath}/${fileName}`;
};

// 异步加载缩略图
const loadThumbnailAsync = async () => {
  try {
    // 重置标志变量
    isUsingDefaultImage.value = false;

    const { resource } = props;
    if (!resource || (resource.list && Array.isArray(resource.list))) {
      thumbnailUrl.value =
        props.resource.type == 15 ? defaultAppCover : DEFAULT_THUMBNAIL;
      return;
    }

    // 添加时间戳参数来避免缓存
    const timestamp = new Date().getTime();

    // 优先使用 poster 字段（现在 poster 包含了封面或自动生成的缩略图）
    if (resource.poster && resource.path) {
      const path = await getResourcePath(
        joinPath(resource.path, resource.poster)
      );
      if (path) {
        thumbnailUrl.value = `${path}?t=${timestamp}`;
        return;
      }
    }

    // 向后兼容：如果没有 poster 但有 thumbnail，尝试使用 thumbnail
    if (
      !resource.poster &&
      resource.thumbnail &&
      resource.path &&
      resource.thumbnail !== "无缩略图"
    ) {
      const path = await getResourcePath(
        joinPath(resource.path, resource.thumbnail)
      );
      if (path) {
        thumbnailUrl.value = `${path}?t=${timestamp}`;
        return;
      }
    }

    // 尝试使用原始文件作为预览（对于图片资源）
    if (
      resource.type === FileType.IMAGE &&
      resource.fileName &&
      resource.path
    ) {
      const path = await getResourcePath(
        joinPath(resource.path, resource.fileName)
      );
      if (path) {
        thumbnailUrl.value = `${path}?t=${timestamp}`;
        return;
      }
    }

    // 默认图片
    thumbnailUrl.value =
      props.resource.type == 15 ? defaultAppCover : DEFAULT_THUMBNAIL;
  } catch (error) {
    if (process.env.NODE_ENV === "development") {
      console.error("ResourceCard: 加载缩略图失败", error);
    }
    thumbnailUrl.value =
      props.resource.type == 15 ? defaultAppCover : DEFAULT_THUMBNAIL;
  }
};

// 监听资源变化，重新加载缩略图
watch(
  () => props.resource,
  () => {
    loadThumbnailAsync();
  },
  { deep: true }
);

// 监听资源变化，强制更新预览图
watch(
  () => props.resource,
  (newResource) => {
    if (newResource) {
      // 强制重新计算缩略图URL
      nextTick(() => {
        // 触发图片重新加载
        const imgElement = document.querySelector(
          `[data-id="${newResource.index || newResource.id}"] img`
        );
        if (imgElement) {
          const currentSrc = imgElement.src;
          imgElement.src = "";
          nextTick(() => {
            imgElement.src = currentSrc;
          });
        }
      });
    }
  },
  { deep: true }
);

// 组件挂载后加载缩略图
onMounted(() => {
  loadThumbnailAsync();
});

// 右键菜单相关
const showMenu = ref(false);
const menuStyle = ref({
  top: "0px",
  left: "0px",
});

// 显示右键菜单
const showContextMenu = (event) => {
  // 如果未启用右键菜单，直接返回
  if (!props.enableContextMenu) {
    return;
  }

  event.preventDefault();
  event.stopPropagation();

  // 使用相对于视口的位置
  menuStyle.value = {
    top: `${event.clientY}px`,
    left: `${event.clientX}px`,
  };

  showMenu.value = true;

  // 点击其他地方关闭菜单
  const closeMenu = (e) => {
    if (e.type === "contextmenu") {
      e.preventDefault();
    }
    showMenu.value = false;
    document.removeEventListener("click", closeMenu);
    document.removeEventListener("contextmenu", closeMenu);
  };

  // 添加点击和右键点击事件监听
  document.addEventListener("click", closeMenu);
  document.addEventListener("contextmenu", closeMenu);
};

// 辅助方法 - 格式化时间
const formatTime = (timeString) => {
  const date = new Date(timeString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};
</script>

<style scoped>
/* 资源卡片基础样式 */
.resource-card {
  position: relative;
  background-color: var(--color-card-background);
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12); /* 加重阴影 */
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 2px solid var(--color-border);
  cursor: pointer;
  width: 100%;
  height: auto;
  aspect-ratio: 1.69;
}

.resource-card:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15); /* 加重悬停时的阴影 */
  border-color: var(--color-primary);
}

.resource-card.selected {
  border-color: var(--color-primary);
  background-color: rgba(0, 0, 0, 0.08);
}

/* 缩略图容器 */
.thumbnail-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  aspect-ratio: 1.69; /* 匹配父容器比例 */
}

/* 缩略图图片 */
.thumbnail-container > img:first-child {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  position: absolute;
  top: 0;
  left: 0;
}
.resource-card:hover .thumbnail-container > img:first-child {
  transform: scale(1.05);
}

/* 资源信息 */
.resource-info {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 12px;
  height: 100%;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.6),
    rgba(0, 0, 0, 0.3),
    transparent
  );
  color: var(--color-white);
  z-index: 1;
}

.resource-info h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;

  color: #fff;
  font-family: FZVariable-LanTingHeiK;
  font-size: 20px;
  font-style: normal;
  font-weight: 480;
  line-height: 119.175%; /* 23.835px */
}

/* 所有模式使用统一样式 */

.tags {
  display: inline-flex;
  gap: 6px;
}

.edit-mode .resource-tags,
.select-mode .resource-tags {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.resource-tags {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.resource-type,
.tag-type {
  display: inline-block;
  padding: 2px 6px;
  background-color: var(--color-primary);
  color: var(--color-white);
  font-size: 12px;
  border-radius: 4px;
}
.tag-type {
  background-color: #f69422;
}
.resource-category {
  display: inline-block;
  padding: 2px 6px;
  background-color: var(--color-warning);
  color: var(--color-white);
  font-size: 12px;
  border-radius: 4px;
}

/* APK 信息样式 */
.apk-info {
  margin-top: 4px;
  display: flex;
  gap: 8px;
  align-items: center;
}

.package-name {
  font-size: 11px;
  color: var(--color-white);
  background-color: rgba(0, 0, 0, 0.5);
  padding: 1px 4px;
  border-radius: 3px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.version-name {
  font-size: 11px;
  color: var(--color-white);
  background-color: var(--color-success);
  padding: 1px 4px;
  border-radius: 3px;
}

/* 控制按钮 */
.card-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
  opacity: 1;
  transition: transform 0.2s ease;
  display: flex;
  gap: 8px;
}

.publish-btn,
.edit-btn,
.stop-btn {
  padding: 4px 10px;
  color: var(--color-white);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  display: flex;
  justify-content: baseline;
}

.edit-btn,
.stop-btn {
  height: 28px;
}

.publish-btn {
  background-color: var(--color-primary);
}
.publish-btn img {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.publish-btn:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.edit-btn:hover {
  background-color: var(--color-warning);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
.stop-btn:hover {
  background-color: var(--color-danger);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: var(--color-box-bg);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  z-index: 9999; /* 提高z-index确保显示在最上层 */
  min-width: 200px;
  margin: 0;
  padding-top: 10px;
  box-sizing: border-box;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.menu-item {
  display: flex;
  justify-content: flex-start;
  padding: 10px 20px;
  flex-direction: row;
  cursor: pointer;
  width: 100%;
  height: 44px;
  margin: 0;
  box-sizing: border-box;
  font-size: 14px;
  color: var(--color-menu-text);
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: var(--color-dialog-background);
}

/* 预览图标样式 */
.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  z-index: 2;
}

.resource-card:hover .preview-overlay {
  opacity: 1;
}

.preview-icon {
  width: 48px;
  height: 48px;
  opacity: 0.8;
  transition: transform 0.3s ease;
}

.preview-overlay:hover .preview-icon {
  transform: scale(1.1);
  opacity: 1;
}

.img-bg-placeholder {
  width: 100%;
  height: 100%;
  background: #d3e7fe;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}
/* 发布状态 */
.card-texts {
  position: absolute;
  bottom: 15px;
  left: 20px;
  z-index: 10;
  opacity: 1;
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  height: 44px;

  color: #d8d8d8;
  font-size: 12px;
  font-style: normal;
  font-weight: 340;
  line-height: normal;
}

.online-status {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  flex: 1;
  overflow: hidden;
}

/* 状态指示器基础样式 */
.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  transition: all 0.3s ease;
  margin-left: 2px; /* 与SN号保持一点距离 */
}

/* 在线状态 - 绿色 */
.status-indicator.online {
  background-color: var(--color-success);
  box-shadow: 0 0 6px var(--color-success);
  animation: pulse 2s infinite;
}

/* 离线状态 - 灰色 */
.status-indicator.offline {
  background-color: #fe2938;
}
</style>
