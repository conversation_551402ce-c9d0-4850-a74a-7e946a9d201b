<template>
  <div v-if="show" class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-content" @click.stop>
      <div class="dialog-header">
        <h4>{{ record.status === "active" ? "添加设备" : "编辑发布记录" }}</h4>
        <button class="btn-close" @click="close">&times;</button>
      </div>

      <div class="dialog-body">
        <div v-if="loading" class="loading-indicator">
          <div class="spinner-small"></div>
          <span>加载中...</span>
        </div>
        <div v-else class="form-container">
          <!-- 记录状态信息 -->
          <div v-if="record" class="record-info-section">
            <div class="record-status-display">
              <span class="label">状态:</span>
              <span
                class="status-badge"
                :class="record.status === 'active' ? 'online' : 'offline'"
              >
                {{ record.status === "active" ? "发布中" : "已停止" }}
              </span>
            </div>
            <div class="record-time-display">
              <span class="label">创建时间:</span>
              <span class="value">{{ formatDate(record.createdAt) }}</span>
            </div>
          </div>

          <!-- 当记录处于发布中状态时，只显示设备选择部分 -->
          <template v-if="record.status === 'active'">
            <div class="form-group">
              <label>已选设备</label>
              <div class="selected-devices">
                <div
                  v-for="sn in selectedDevices"
                  :key="sn"
                  class="selected-device"
                >
                  <span>{{ getDeviceName(sn) }}</span>
                  <span class="device-sn">(SN: {{ sn }})</span>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label>添加设备</label>
              <div class="device-list">
                <div
                  v-for="device in availableDevices"
                  :key="device.sn"
                  class="device-item"
                  :class="{ selected: selectedDevices.includes(device.sn) }"
                  @click="toggleDevice(device.sn)"
                >
                  <span class="device-id">{{ device.id || "未命名设备" }}</span>
                  <span class="device-sn">(SN: {{ device.sn }})</span>
                </div>
              </div>
            </div>
          </template>
          <!-- 当记录已停止时，显示完整的编辑界面 -->
          <template v-else>
            <!-- 资源和设备选择（并排布局） -->
            <div class="form-row">
              <!-- 资源选择（单选框） -->
              <div
                class="form-group form-group-half"
                v-if="resources.length > 0"
              >
                <label>资源</label>
                <div class="radio-list">
                  <div
                    v-for="option in resourceOptions"
                    :key="option.value"
                    class="radio-item"
                    :class="{
                      'item-selected': selectedResource === option.value,
                    }"
                  >
                    <label class="radio-label">
                      <input
                        type="radio"
                        :value="option.value"
                        v-model="selectedResource"
                        class="radio-input"
                      />
                      <span class="radio-text">{{ option.label }}</span>
                    </label>
                  </div>
                </div>
                <div class="form-hint">选择要发布的资源</div>
                <div class="selection-count" v-if="selectedResource !== null">
                  已选择资源: {{ getResourceLabel(selectedResource) }}
                </div>
              </div>

              <!-- 设备选择（复选框） -->
              <div class="form-group form-group-half" v-if="devices.length > 0">
                <label>设备</label>
                <div class="checkbox-list">
                  <div
                    v-for="option in deviceOptions"
                    :key="option.value"
                    class="checkbox-item"
                    :class="{
                      'item-selected': form.devices.includes(option.value),
                      'item-disabled': option.disabled,
                    }"
                    :title="option.tooltip"
                  >
                    <label class="checkbox-label">
                      <input
                        type="checkbox"
                        :value="option.value"
                        v-model="form.devices"
                        class="checkbox-input"
                        :disabled="option.disabled"
                      />
                      <span class="checkbox-text">{{ option.label }}</span>
                    </label>
                  </div>
                </div>
                <div class="form-hint">
                  选择要发布到的设备（已在其他播控中的设备不可选）
                </div>
                <div class="selection-count" v-if="form.devices.length > 0">
                  已选择 {{ form.devices.length }} 台设备
                </div>
              </div>
            </div>

            <!-- 循环播放 -->
            <div class="form-group loop-play-group">
              <div class="loop-play-header">
                <label>循环播放</label>
                <div class="toggle-switch-container">
                  <label class="toggle-switch">
                    <input type="checkbox" v-model="form.loopPlay" />
                    <span class="toggle-slider"></span>
                  </label>
                  <span class="toggle-label">{{
                    form.loopPlay ? "已启用" : "已禁用"
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 停止时间 -->
            <div
              class="form-group stop-time-group"
              v-if="record && record.stoppedAt"
            >
              <label>停止时间</label>
              <div class="info-display">
                <i class="icon-time"></i>
                <span>{{ formatDate(record.stoppedAt) }}</span>
              </div>
              <div class="form-hint">此记录已于上述时间停止发布</div>
            </div>
          </template>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="btn btn-secondary" @click="close">取消</button>
        <button
          class="btn btn-primary"
          :disabled="record.status === 'active' && !hasNewDevices"
          @click="saveRecord"
        >
          {{ record.status === "active" ? "添加" : "保存" }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useElectronAPI } from "../plugins/electron";
import alertService from "../plugins/alert";
import messageService from "../plugins/message";

// 获取Electron API
const electronAPI = useElectronAPI();

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  record: {
    type: Object,
    default: null,
  },
  resources: {
    type: Array,
    default: () => [],
  },
  devices: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits(["close", "save"]);

// 状态
const loading = ref(false);
const saving = ref(false);
const selectedResource = ref(null);
const form = ref({
  resources: [],
  devices: [],
  loopPlay: false,
});

// 计算属性
const title = computed(() => (props.record ? "编辑发布记录" : "新建发布记录"));

const resourceOptions = computed(() => {
  return props.resources.map((resource, index) => ({
    label: resource.showName || resource.fileName || `资源 ${index + 1}`,
    value: index,
  }));
});

// 获取所有发布记录中的设备
const publishedDevices = ref([]);

// 获取所有发布记录中的设备
const fetchPublishedDevices = async () => {
  try {
    // 获取所有发布记录
    const publishRecords = await electronAPI.getPublishRecords();

    // 提取所有发布记录中的设备（无论状态）
    const devices = [];
    publishRecords.forEach((record) => {
      // 跳过当前正在编辑的记录
      if (props.record && record.id === props.record.id) {
        return;
      }

      if (record.devices && Array.isArray(record.devices)) {
        record.devices.forEach((sn) => {
          if (!devices.includes(sn)) {
            devices.push(sn);
          }
        });
      }
    });

    // 更新发布设备列表
    publishedDevices.value = devices;
    console.log("其他发布记录中的设备:", publishedDevices.value);
  } catch (error) {
    console.error("获取发布设备失败:", error);
  }
};

// 在组件挂载时获取已发布设备
onMounted(() => {
  fetchPublishedDevices();
});

// 监听 show 变化，每次打开对话框时刷新已发布设备列表
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      fetchPublishedDevices();
    }
  }
);

const deviceOptions = computed(() => {
  return props.devices.map((device) => {
    const isPublished = publishedDevices.value.includes(device.sn);
    const isInCurrentRecord =
      props.record &&
      props.record.devices &&
      props.record.devices.includes(device.sn);

    return {
      label: device.name || device.sn || "未命名设备",
      value: device.sn,
      disabled: isPublished && !isInCurrentRecord, // 如果设备在其他播控中且不在当前记录中，则禁用
      tooltip:
        isPublished && !isInCurrentRecord ? "该设备已在其他播控中使用" : "",
    };
  });
});

const isFormValid = computed(() => {
  return form.value.resources.length > 0 && form.value.devices.length > 0;
});

// 监听 record 变化，更新表单
watch(
  () => props.record,
  (newRecord) => {
    if (newRecord) {
      // 更新表单数据
      form.value = {
        resources: [], // 将在下面设置为单个资源
        devices: newRecord.devices || [],
        loopPlay: newRecord.loopPlay || false,
      };

      // 设置选中的资源（单选）
      if (newRecord.resources && newRecord.resources.length > 0) {
        selectedResource.value = newRecord.resources[0]; // 只取第一个资源
      } else {
        selectedResource.value = null;
      }
    } else {
      // 新建记录时重置表单
      form.value = {
        resources: [],
        devices: [],
        loopPlay: false,
      };
      selectedResource.value = null;
    }
  },
  { immediate: true }
);

// 监听 show 变化
watch(
  () => props.show,
  (newShow) => {
    // 可以在这里添加需要在 show 变化时执行的逻辑
  }
);

// 监听 selectedResource 变化，更新 form.resources
watch(
  () => selectedResource.value,
  (newValue) => {
    if (newValue !== null) {
      form.value.resources = [newValue]; // 将单选的资源设置到 resources 数组中
    } else {
      form.value.resources = [];
    }
  }
);

// 组件挂载时
onMounted(() => {
  // 组件初始化逻辑
});

// 方法
const close = () => {
  emit("close");
};

// 处理遮罩层点击
const handleOverlayClick = (event) => {
  // 点击遮罩层时关闭对话框
  if (event.target.classList.contains("dialog-overlay")) {
    close();
  }
};

const saveRecord = async () => {
  if (props.record.status === "active" && !hasNewDevices.value) {
    alertService.alert({
      title: "提示",
      message: "请选择要添加的设备",
      confirmButtonText: "确定",
    });
    return;
  }

  try {
    saving.value = true;

    // 创建一个简单的数据对象，确保可以被序列化
    const recordData = {
      // 确保 resources 是简单的数组，不包含复杂对象
      resources: [...(props.record.resources || [])],
      // 使用 selectedDevices 而不是 form.devices
      devices: [...selectedDevices.value],
      // 布尔值可以直接传递
      loopPlay: props.record.loopPlay || false,
    };

    // 使用 JSON 序列化和反序列化确保数据是可克隆的
    const cleanData = JSON.parse(JSON.stringify(recordData));

    let result;
    if (props.record) {
      // 更新现有记录
      result = await electronAPI.updatePublishRecord(
        props.record.id,
        cleanData
      );

      // 如果是活跃记录，向新添加的设备发送播控命令
      if (props.record.status === "active") {
        // 找出新添加的设备
        const newDevices = selectedDevices.value.filter(
          (sn) => !props.record.devices.includes(sn)
        );

        if (
          newDevices.length > 0 &&
          props.record.resources &&
          props.record.resources.length > 0
        ) {
          // 向每个新设备发送播控命令
          for (const resourceIndex of props.record.resources) {
            await electronAPI.publishResource(
              resourceIndex,
              newDevices,
              props.record.loopPlay || false
            );
          }
        }
      }
      messageService.success("发布记录更新成功");
    } else {
      // 创建新记录
      result = await electronAPI.createPublishRecord(cleanData);
      messageService.success("发布记录创建成功");
    }

    // 触发保存事件
    emit("save", result);

    // 关闭对话框
    close();
  } catch (error) {
    console.error("[EditRecordDialog] 保存发布记录失败:", error);
    await alertService.alert({
      title: "保存失败",
      message: `保存发布记录失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  } finally {
    saving.value = false;
  }
};

// 获取资源标签
const getResourceLabel = (resourceIndex) => {
  if (resourceIndex === null) return "";
  const option = resourceOptions.value.find(
    (opt) => opt.value === resourceIndex
  );
  return option ? option.label : `资源 ${resourceIndex}`;
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

// 选中的设备列表
const selectedDevices = ref([]);

// 初始化选中的设备
const initSelectedDevices = () => {
  if (props.record && props.record.devices) {
    selectedDevices.value = [...props.record.devices];
  }
};

// 计算可用的设备列表（排除已选设备和已在其他播控中的设备）
const availableDevices = computed(() => {
  if (!props.devices) return [];

  if (props.record.status === "active") {
    // 发布中状态只显示未选择的在线设备，且不在其他播控中
    return props.devices.filter(
      (device) =>
        device.isOnline &&
        device.isAdded &&
        !props.record.devices.includes(device.sn) &&
        !publishedDevices.value.includes(device.sn)
    );
  } else {
    // 已停止状态显示所有在线设备，但排除已在其他播控中的设备
    return props.devices.filter(
      (device) =>
        device.isOnline &&
        device.isAdded &&
        !publishedDevices.value.includes(device.sn)
    );
  }
});

// 是否有新添加的设备
const hasNewDevices = computed(() => {
  if (!props.record || !props.record.devices) return false;
  return selectedDevices.value.length > props.record.devices.length;
});

// 切换设备选择
const toggleDevice = (sn) => {
  if (props.record.status === "active") {
    // 发布中状态只允许添加设备
    if (!selectedDevices.value.includes(sn)) {
      selectedDevices.value.push(sn);
    }
  } else {
    // 已停止状态允许添加和移除设备
    const index = selectedDevices.value.indexOf(sn);
    if (index === -1) {
      selectedDevices.value.push(sn);
    } else {
      selectedDevices.value.splice(index, 1);
    }
  }
};

// 获取设备名称
const getDeviceName = (sn) => {
  const device = props.devices.find((d) => d.sn === sn);
  return device ? device.id || device.sn : sn;
};

// 监听显示状态变化
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      initSelectedDevices();
    }
  }
);
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; /* 提高z-index，确保在最上层 */
  animation: fadeIn 0.2s ease-out; /* 添加淡入动画 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-content {
  background-color: var(--color-background-light, #fff);
  border-radius: var(--border-radius, 8px);
  box-shadow: var(--shadow-lg, 0 4px 12px rgba(0, 0, 0, 0.15));
  width: 600px; /* 增加宽度，提供更多空间 */
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.3s ease-out; /* 添加滑入动画 */
  position: relative; /* 确保定位正确 */
  z-index: 10000; /* 确保内容在遮罩之上 */
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md, 16px);
  border-bottom: 1px solid var(--color-border, #eee);
}

.dialog-header h4 {
  margin: 0;
  font-size: 18px;
  color: var(--color-text-primary, #333);
}

.btn-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-secondary, #999);
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.dialog-body {
  padding: var(--spacing-md, 16px);
  overflow-y: auto;
  flex: 1;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm, 8px);
  padding: var(--spacing-md, 16px);
  border-top: 1px solid var(--color-border, #eee);
}

/* 记录信息部分 */
.record-info-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md, 16px);
  padding: var(--spacing-md, 16px);
  background-color: var(--color-background, #f9f9f9);
  border-radius: var(--border-radius, 8px);
  border: 1px solid var(--color-border, #eee);
  box-shadow: var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.05));
}

.record-status-display,
.record-time-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm, 8px);
}

.label {
  color: var(--color-text-secondary, #666);
  font-size: 14px;
}

.value {
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.online {
  background-color: var(--color-success-bg);
  color: var(--color-success);
  border: 1px solid var(--color-success);
}

.status-badge.offline {
  background-color: var(--color-danger-bg);
  color: var(--color-danger);
  border: 1px solid var(--color-danger);
}

/* 暗色主题状态标签 */
[data-theme="dark"] .status-badge.online {
  background-color: var(--color-success-bg);
  color: var(--color-success-light);
  border-color: var(--color-success);
}

[data-theme="dark"] .status-badge.offline {
  background-color: var(--color-danger-bg);
  color: var(--color-danger-light);
  border-color: var(--color-danger);
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  gap: var(--spacing-md, 16px);
  color: var(--color-text-secondary, #666);
}

.spinner-small {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary, #1890ff);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 表单样式 */
.form-container {
  margin-top: var(--spacing-xs, 4px);
}

.form-row {
  display: flex;
  gap: var(--spacing-md, 16px);
  margin-bottom: var(--spacing-md, 16px);
}

.form-group {
  margin-bottom: var(--spacing-md, 16px);
}

.form-group-half {
  flex: 1;
  min-width: 0; /* 允许内容收缩 */
}

.selection-count {
  margin-top: var(--spacing-xs, 4px);
  font-size: 12px;
  color: var(--color-primary);
  font-weight: 600;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs, 4px);
  font-weight: 600;
  color: var(--color-text-primary, #333);
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: all 0.2s;
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-bg, rgba(24, 144, 255, 0.2));
  outline: none;
}

.form-hint {
  margin-top: 4px;
  font-size: 12px;
  color: var(--color-text-secondary);
}

.radio-list,
.checkbox-list {
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm, 8px);
  background-color: var(--color-background-light);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05); /* 添加内阴影，增强立体感 */
}

.radio-item,
.checkbox-item {
  margin-bottom: var(--spacing-xs, 4px);
  padding: var(--spacing-xs, 4px) var(--spacing-sm, 8px);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s;
  border: 1px solid transparent; /* 透明边框，为选中状态做准备 */
}

.radio-item:hover,
.checkbox-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

/* 选中状态样式 */
.radio-item:has(input:checked),
.checkbox-item:has(input:checked),
.item-selected {
  background-color: rgba(var(--color-primary-rgb, 24, 144, 255), 0.05);
  border-color: var(--color-primary-light);
}

.radio-label,
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal;
  width: 100%;
}

.radio-input,
.checkbox-input {
  margin-right: var(--spacing-sm, 8px);
  width: 18px;
  height: 18px;
  accent-color: var(--color-primary); /* 设置选中颜色 */
  cursor: pointer;
  border: 2px solid var(--color-border); /* 增加边框宽度 */
  border-radius: 3px; /* 为复选框添加圆角 */
  background-color: var(--color-background-light); /* 设置背景色 */
  appearance: none; /* 移除默认样式 */
  -webkit-appearance: none; /* Safari 兼容 */
  position: relative; /* 为自定义标记做准备 */
  display: inline-block; /* 确保显示正确 */
  vertical-align: middle; /* 垂直对齐 */
  box-sizing: border-box; /* 确保尺寸计算正确 */
}

/* 自定义复选框标记 */
.checkbox-input:checked::before {
  content: "✓"; /* 使用对勾符号 */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  color: white;
  font-weight: bold;
}

/* 自定义单选框标记 */
.radio-input {
  border-radius: 50%; /* 单选框是圆形的 */
}

.radio-input:checked::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
}

.radio-text,
.checkbox-text {
  font-size: 14px;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1; /* 让文本占据剩余空间 */
  font-weight: normal; /* 确保文本不会太粗 */
}

/* 暗色主题表单控件样式 */
[data-theme="dark"] .form-control {
  background-color: var(--color-background);
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .form-control:focus {
  border-color: var(--color-primary-light);
  box-shadow: 0 0 0 2px var(--color-primary-bg);
}

[data-theme="dark"] .radio-list,
[data-theme="dark"] .checkbox-list {
  background-color: var(--color-background);
  border-color: var(--color-border-dark);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2); /* 暗色主题下的内阴影更明显 */
}

[data-theme="dark"] .radio-item:hover,
[data-theme="dark"] .checkbox-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* 暗色主题下的选中状态 */
[data-theme="dark"] .radio-item:has(input:checked),
[data-theme="dark"] .checkbox-item:has(input:checked),
[data-theme="dark"] .item-selected {
  background-color: rgba(var(--color-primary-rgb, 24, 144, 255), 0.15);
  border-color: var(--color-primary);
}

[data-theme="dark"] .radio-text,
[data-theme="dark"] .checkbox-text {
  color: var(--color-text-primary-dark);
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3); /* 添加文本阴影，增强可读性 */
}

[data-theme="dark"] .radio-input,
[data-theme="dark"] .checkbox-input {
  accent-color: var(--color-primary-light); /* 暗色主题下的选中颜色 */
  background-color: var(--color-background-dark);
  border-color: var(--color-border-dark);
  border-width: 2px; /* 增加边框宽度，使其更加明显 */
}

/* 暗色主题下的复选框标记 */
[data-theme="dark"] .checkbox-input:checked {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary-light);
}

/* 暗色主题下的单选框标记 */
[data-theme="dark"] .radio-input:checked {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary-light);
}

/* 这里我们已经移除了旧的复选框样式，使用新的样式代替 */

/* 信息显示 */
.info-display {
  padding: 10px 12px;
  background-color: var(--color-background, #f9f9f9);
  border-radius: var(--border-radius, 4px);
  border: 1px solid var(--color-border, #eee);
  color: var(--color-text-primary, #333);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm, 8px);
}

.stop-time-group .info-display {
  background-color: rgba(var(--color-danger-rgb, 220, 53, 69), 0.05);
  border-color: rgba(var(--color-danger-rgb, 220, 53, 69), 0.2);
}

.icon-time {
  color: var(--color-danger);
  font-size: 16px;
}

[data-theme="dark"] .stop-time-group .info-display {
  background-color: rgba(var(--color-danger-rgb, 220, 53, 69), 0.1);
  border-color: rgba(var(--color-danger-rgb, 220, 53, 69), 0.3);
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast, 0.15s);
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-background-dark);
  border-color: var(--color-primary);
}

/* 暗色主题按钮样式增强 */
[data-theme="dark"] .btn {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .btn-primary {
  background-color: var(--color-primary);
  border: 1px solid var(--color-primary-light);
}

[data-theme="dark"] .btn-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary);
}

[data-theme="dark"] .btn-secondary {
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border-light);
}

[data-theme="dark"] .btn-secondary:hover {
  background-color: var(--color-background-dark);
  border-color: var(--color-primary-light);
}

/* 循环播放样式 */
.loop-play-group {
  background-color: var(--color-background, #f9f9f9);
  border-radius: var(--border-radius, 8px);
  padding: var(--spacing-md, 16px);
  border: 1px solid var(--color-border, #eee);
}

.loop-play-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs, 4px);
}

.loop-play-header label {
  margin-bottom: 0;
}

.toggle-switch-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm, 8px);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 22px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 22px;
  transition: 0.4s;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: 0.4s;
}

input:checked + .toggle-slider {
  background-color: var(--color-primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(18px);
}

.toggle-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-secondary);
}

input:checked ~ .toggle-label {
  color: var(--color-primary);
}

/* 暗色主题循环播放样式 */
[data-theme="dark"] .loop-play-group {
  background-color: var(--color-background);
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .toggle-slider {
  background-color: #555;
}

[data-theme="dark"] .toggle-slider:before {
  background-color: var(--color-background-light);
}

[data-theme="dark"] input:checked + .toggle-slider {
  background-color: var(--color-primary-light);
}

/* 选中的设备部分样式 */
.selected-devices {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.selected-device {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  padding: 8px 12px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-sm);
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.device-item {
  padding: 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-sm);
}

.device-item:hover {
  background-color: var(--color-background);
  border-color: var(--color-primary-light);
}

.device-item.selected {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-white);
}

.device-id {
  font-weight: 500;
}

.device-sn {
  color: var(--color-text-secondary);
  font-size: 0.9em;
}

.device-item.selected .device-sn {
  color: rgba(255, 255, 255, 0.8);
}

.selected-device {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  padding: 8px 12px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-sm);
}

[data-theme="dark"] .device-sn {
  color: var(--color-text-secondary-dark);
}

[data-theme="dark"] .device-item.selected .device-sn {
  color: rgba(255, 255, 255, 0.8);
}

.checkbox-item.item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkbox-item.item-disabled .checkbox-label {
  cursor: not-allowed;
}

.checkbox-item.item-disabled .checkbox-input {
  cursor: not-allowed;
}
</style>
