<template>
  <div class="page">
    <div class="header">
      <div
        v-if="
          solutions.length === 0 ||
          (!showResourceManager && !onlineDeployment && !offlineDeployment)
        "
        class="header-title"
      >
        方案管理
      </div>

      <div
        v-else-if="onlineDeployment || offlineDeployment"
        class="header-top"
        :class="{ online: onlineDeployment }"
      >
        <button class="back-btn" @click="backFromDeploy">
          <img src="@assets/svg/back_dark.svg" alt="back" class="back-icon" />
          <span
            >部署方案-{{ offlineDeployment ? "离线部署" : "在线部署" }}</span
          >
        </button>
      </div>

      <div v-else class="header-actions">
        <div class="header-left">
          <button class="back-btn">
            <img
              src="@assets/svg/back_light.svg"
              alt="back"
              @click="backToSolutionList"
            />
          </button>
          <div class="header-desc">
            <div class="header-desc-title">
              {{
                currentSolution
                  ? currentSolution.name || "播控方案"
                  : "播控方案"
              }}
              <img
                src="@assets/svg/solution_edit.svg"
                @click="handleEditSolution"
              />
            </div>
            <div class="header-desc-content">
              {{
                currentSolution
                  ? currentSolution.description || "暂无描述"
                  : "这里是方案描述"
              }}
            </div>
          </div>
        </div>

        <button
          class="btn btn-danger"
          @click="confirmDeleteSolution"
          :disabled="!currentSolution"
          v-if="solutions.length > 0"
        >
          删除方案
        </button>
      </div>
    </div>

    <OnlineDeployPanel
      v-if="onlineDeployment"
      :devices="onlineDeployDevices"
      @deploy="handleDeployOne"
      @cancel-deploy="handleCancelDeployOne"
      @batch-deploy="handleBatchDeploy"
    />

    <OfflineDeployment v-else-if="offlineDeployment" />

    <div v-else class="content-panel" :style="contentPanelStyle">
      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>正在加载方案数据...</p>
      </div>

      <div v-else-if="error" class="error">
        <p>{{ error }}</p>
        <button class="btn btn-primary" @click="refreshSolutions">重试</button>
      </div>

      <div v-else-if="solutions.length === 0">
        <SolutionCard :is-empty="true" title="播控">
          <template #actions>
            <button class="btn btn-primary" @click="createNewSolution">
              新建方案
            </button>
          </template>
        </SolutionCard>
      </div>

      <div v-else-if="!showResourceManager" class="solution-list">
        <SolutionCard
          v-for="solution in solutions"
          :key="solution.UUID"
          title="播控"
          description=""
        >
          <template #actions>
            <div class="solution-actions">
              <button class="btn btn-primary" @click="editSolution(solution)">
                修改方案
              </button>
              <button
                class="btn btn-primary"
                @click="showDeployOptions(solution)"
              >
                部署
              </button>
            </div>
          </template>
          <template #time
            ><span>修改时间: {{ formatDate(solution.updatedAt) }}</span>
          </template>
        </SolutionCard>
      </div>

      <ResourceManager
        v-else
        :solution="currentSolution"
        :enable-subcategories="enableSubcategories"
        :selected-group="selectedGroup"
        @update:solution="
          async (updatedFields) => {
            try {
              // 只更新必要的字段
              const updateData = {
                UUID: updatedFields.UUID,
                name: updatedFields.name,
                description: updatedFields.description,
                updatedAt: Date.now(),
              };

              // 直接调用API更新方案
              await electronAPI.updateSolution(updateData);

              // 刷新方案列表
              await fetchSolutions();
              if (updatedSolution) {
                currentSolution.value = updatedSolution;
              }
            } catch (error) {
              console.error('更新方案失败:', error);
              await showAlert(
                '错误',
                `更新方案失败: ${error.message || '未知错误'}`
              );
            }
          }
        "
        @deploy="deploySolution"
        @export="exportSolution"
        @add-resource="showAddResourceDialog"
        @edit-resource="editResource"
        @delete-resource="deleteResource"
        @delete-group="
          (groupName, deleteResources) =>
            deleteGroup(groupName, deleteResources)
        "
        @add-group="handleAddGroup"
        @edit-group="handleEditGroup"
        @select-group="selectGroup"
        @back="backToSolutionList"
        ref="resourceManager"
      />
    </div>

    <!-- 部署选项对话框 -->
    <div
      v-if="showDeployDialog"
      class="deploy-dialog-overlay"
      @click="closeDeployDialog"
    >
      <div class="deploy-dialog" @click.stop>
        <div class="deploy-dialog-header">
          <img
            class="btn-close"
            @click="closeDeployDialog"
            src="@assets/svg/close.svg"
          />
          <div class="dialog-title">部署类型</div>
        </div>
        <div class="deploy-dialog-content">
          <div class="deploy-option" @click="handleDeploy('online')">
            <i class="icon-cloud"></i>
            <div class="deploy-option-info">
              <h4>在线部署</h4>
              <pre>
设备到货后，在以下场景建议使用在线部署：
1. 首次部署方案时，方案内容和设备数量较少
2. 整体方案不超过 10G
3. 更新方案时，仅发生少量内容增减/修改
              </pre>
            </div>
          </div>
          <div class="deploy-option" @click="handleDeploy('offline')">
            <i class="icon-local"></i>
            <div class="deploy-option-info">
              <h4>离线部署</h4>
              <pre>
以下场景建议使用离线部署：
1.首次部署方案时，方案内容和设备数量较多
2.整体方案大于 10G
3.更新方案时，发生较多内容增减/修改
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
let transferHandler = null;
let errorHandler = null;
import {
  ref,
  computed,
  onMounted,
  reactive,
  nextTick,
  watch,
  h,
  onUnmounted,
} from "vue";
import { useElectronAPI } from "../plugins/electron";
import alertService from "../plugins/alert";
import DevicePanel from "../components/DevicePanel.vue";
import ResourceManager from "../components/ResourceManager.vue";
import resourceDialogService from "../services/ResourceDialogService";
import { isSubcategory, createFullGroupName } from "../utils/group-utils";
import { DEFAULT_GROUPS } from "../../../shared/constants/defaults.js";
import SolutionCard from "../components/SolutionCard.vue";
import defaultBgImage from "../../assets/images/yvr_playcontrol_back.png";
import messageService from "../plugins/message";
import { fa } from "element-plus/es/locales.mjs";
import OnlineDeployPanel from "../components/OnlineDeployPanel.vue";
import { useDeployStore } from "../stores/deploy";
import OfflineDeployment from "../components/OfflineDeployment.vue";

// 使用Electron API
const electronAPI = useElectronAPI();
const deployStore = useDeployStore();

// 状态
const solutions = ref([]);
const currentSolution = ref(null);
const selectedGroup = ref("全部");
const loading = ref(false);
const resourceLoading = ref(false); // 资源区域加载状态
const resourceLoadingText = ref("加载中"); // 资源加载文本
const error = ref(null);
const resourceManager = ref(null); // ResourceManager 组件引用
const showResourceManager = ref(false);
const showDeployDialog = ref(false);
const onlineDeployment = ref(false);
const offlineDeployment = ref(false);
const selectedDevices = ref([]);
const completedDevices = ref([]);
const failedDevices = ref([]);
const backgroundUrl = ref("");
const onlineDeployDevices = ref([]);

// 添加全局状态控制
const isDeploying = computed(() => {
  return onlineDeployDevices.value.some(
    (device) => device.deployStatus === "部署中"
  );
});

// 监听部署状态变化
watch(isDeploying, (newValue) => {
  // 设置全局状态，用于控制导航
  window.isDeploying = newValue;
});

// 注意：筛选和搜索变量已移除，因为它们在当前UI中不再使用
// 筛选和搜索逻辑已移至 ResourceManager 组件中

// 注意：以下计算属性已移除，因为它们在当前UI中不再使用
// - hierarchicalGroups
// - mainTypeOptions

// 计算属性 - 是否启用子分类功能
const enableSubcategories = computed(() => {
  // 这里可以根据配置或其他条件决定是否启用子分类
  // 目前默认启用
  return true;
});

/**
 * 获取设置
 * @returns {Promise<void>}
 */
const fetchSettings = async () => {
  try {
    // 获取设置，但不再需要设置 enableSubcategories，因为它现在是计算属性
    const settings = await electronAPI.getSettings();
    console.log("获取设置成功:", settings);
  } catch (error) {
    console.error("获取设置失败:", error);
  }
};

/**
 * 加载方案列表
 * @returns {Promise<void>}
 */
const fetchSolutions = async () => {
  try {
    loading.value = true;
    error.value = null;

    // 获取方案列表
    const solutionList = await electronAPI.getSolutions();

    if (!solutionList || solutionList.length === 0) {
      solutions.value = [];
      currentSolution.value = null;
      loading.value = false;
      return;
    }

    // 处理不同的返回格式
    let processedSolutions = [];

    if (Array.isArray(solutionList)) {
      // 如果直接返回数组
      processedSolutions = solutionList;
    } else if (solutionList.list && Array.isArray(solutionList.list)) {
      // 如果返回对象中包含list数组
      processedSolutions = solutionList.list;
    } else {
      // 如果是单个对象，包装成数组
      processedSolutions = [solutionList];
    }

    // 更新方案列表
    solutions.value = processedSolutions;

    // 如果有方案，选择第一个或保持当前选择
    if (solutions.value.length > 0) {
      // 如果已经有选中的方案，尝试在新列表中找到它
      if (currentSolution.value) {
        const existingSolution = solutions.value.find(
          (s) => s.UUID === currentSolution.value.UUID
        );
        if (existingSolution) {
          // 保持当前选中的方案和展开状态
          currentSolution.value = existingSolution;
          // 不改变 selectedGroup 和 showResources 的值，保持用户的选择
        } else {
          // 如果找不到当前方案，选择第一个
          currentSolution.value = solutions.value[0];
          selectedGroup.value = "全部";
          // 保持展开状态不变
        }
      } else {
        // 如果没有选中的方案，选择第一个
        currentSolution.value = solutions.value[0];
        selectedGroup.value = "全部";
      }
    }
  } catch (err) {
    error.value = err.message || "获取方案列表失败";
    console.error("获取方案列表失败:", err);
  } finally {
    loading.value = false;
  }
};

/**
 * 刷新方案列表
 */
const refreshSolutions = () => {
  fetchSolutions();
};

/**
 * 选择方案
 * @param {Object} solution - 要选择的方案对象
 * @param {boolean} [keepState=false] - 是否保持当前的展开状态和分组选择
 */
const selectSolution = (solution, keepState = false) => {
  currentSolution.value = solution;
  showResourceManager.value = true;

  if (!keepState) {
    selectedGroup.value = "全部";
  }
};

/**
 * 选择分组
 * @param {string} group - 要选择的分组名称
 */
const selectGroup = (group) => {
  selectedGroup.value = group;
};

/**
 * 更新方案数据
 * @param {Object} updatedFields - 需要更新的字段
 */
const updateSolution = async (updatedFields) => {
  try {
    // 只发送必要的字段
    const updateData = {
      UUID: currentSolution.value.UUID,
      name: updatedFields.name,
      description: updatedFields.description,
      updatedAt: updatedFields.updatedAt,
    };

    // 保存到后端
    await electronAPI.updateSolution(updateData);

    // 刷新方案列表
    await fetchSolutions();

    // 更新当前方案
    const updatedSolution = solutions.value.find(
      (s) => s.UUID === currentSolution.value.UUID
    );
    if (updatedSolution) {
      currentSolution.value = updatedSolution;
    }
  } catch (err) {
    console.error("更新方案失败:", err);
    throw err;
  }
};

/**
 * 处理编辑方案
 * @returns {Promise<void>}
 */
const handleEditSolution = async () => {
  if (!currentSolution.value) return;

  try {
    // 创建一个自定义组件用于编辑
    const EditForm = {
      props: ["modelValue"],
      emits: ["update:modelValue"],
      setup(props, { emit }) {
        const form = ref({
          name: currentSolution.value.name || "",
          description: currentSolution.value.description || "",
        });

        // 初始化时触发一次更新
        emit("update:modelValue", form.value);

        // 监听表单变化并更新
        watch(
          form,
          (newVal) => {
            emit("update:modelValue", newVal);
          },
          { deep: true }
        );

        return () =>
          h("div", { class: "edit-form" }, [
            h("div", { class: "form-group" }, [
              h("input", {
                class: "edit-input",
                value: form.value.name,
                placeholder: "请输入方案标题",
                onInput: (e) => {
                  form.value.name = e.target.value;
                  emit("update:modelValue", form.value);
                },
              }),
            ]),
            h("div", { class: "form-group" }, [
              h("textarea", {
                class: "edit-textarea",
                value: form.value.description,
                placeholder: "请输入方案描述",
                rows: 3,
                onInput: (e) => {
                  form.value.description = e.target.value;
                  emit("update:modelValue", form.value);
                },
              }),
            ]),
          ]);
      },
    };

    // 创建响应式数据存储表单值
    const formData = ref({
      name: currentSolution.value.name || "",
      description: currentSolution.value.description || "",
    });

    // 显示编辑对话框
    const dialogResult = await alertService.customDialog({
      title: "播控名称",
      component: EditForm,
      width: "500px",
      confirmButtonText: "确定",
      modelValue: formData,
      showCancelButton: false,
    });

    // 检查是否点击了确认按钮
    if (dialogResult && formData.value) {
      // 只更新必要的字段
      const updateData = {
        UUID: currentSolution.value.UUID,
        name: formData.value.name.trim(),
        description: formData.value.description.trim(),
        updatedAt: Date.now(),
      };

      // 更新方案
      await electronAPI.updateSolution(updateData);

      // 刷新方案列表
      await fetchSolutions();

      // 显示成功消息
      messageService.success("方案更新成功");
    }
  } catch (error) {
    console.error("编辑方案失败:", error);
    await showAlert("错误", `编辑方案失败: ${error.message || "未知错误"}`);
  }
};

/**
 * 处理分组保存事件 - 已移除，使用内联编辑代替
 */

/**
 * 处理编辑分组事件
 * @param {Object} groupData - 分组数据，包含 oldName 和 newName
 * @returns {Promise<void>}
 */
const handleEditGroup = async (groupData) => {
  if (!currentSolution.value) {
    messageService.warning("请先设置一个播控方案");
    return;
  }

  try {
    loading.value = true;

    // 更新分组
    await electronAPI.updateSolutionGroup(
      currentSolution.value.UUID,
      groupData.oldName,
      groupData.newName,
      groupData.description || ""
    );

    // 显示成功消息
    messageService.success("分组更新成功");

    // 如果当前选中的是被更新的分组，更新选中的分组名称
    if (selectedGroup.value === groupData.oldName) {
      selectedGroup.value = groupData.newName;
    }

    // 刷新方案列表
    await fetchSolutions();
  } catch (err) {
    await handleError("更新分组失败", err);
  } finally {
    loading.value = false;
  }
};

/**
 * 切换资源区域的显示
 */
const toggleResources = () => {
  // 不需要实现，因为 showResources 状态已移除
};

/**
 * 创建新方案
 * @returns {Promise<void>}
 */
const createNewSolution = async () => {
  try {
    // 显示加载状态
    loading.value = true;

    try {
      // 获取当前应用版本
      let appVersion = "1.0.0";
      try {
        appVersion = await electronAPI.getVersion();
      } catch (err) {
        console.error("获取应用版本失败:", err);
      }

      // 准备创建方案的数据，使用默认值
      const solutionData = {
        name: "播控方案",
        description: "",
        files: [], // 初始没有文件
        pcVersion: appVersion, // 确保传递当前应用版本
      };

      console.log("创建方案数据:", solutionData);

      // 调用API创建方案
      const createdSolution = await electronAPI.createSolution(solutionData);

      if (!createdSolution) {
        throw new Error("创建方案失败，未返回方案数据");
      }

      messageService.success("方案创建成功");

      // 刷新方案列表
      await fetchSolutions();

      // 选择新创建的方案
      const solution = solutions.value.find(
        (s) => s.UUID === createdSolution.UUID
      );
      if (solution) {
        // 选择方案并显示资源管理器
        currentSolution.value = solution;
        showResourceManager.value = true;
        // 设置默认选中第一个分组
        selectedGroup.value = solution.groups?.[0] || DEFAULT_GROUPS[0];
      } else {
        console.warn("无法找到新创建的方案:", createdSolution.UUID);
      }
    } catch (err) {
      console.error("创建方案失败:", err);
      await showAlert("错误", `创建方案失败: ${err.message || "未知错误"}`);
    } finally {
      loading.value = false;
    }
  } catch (err) {
    console.error("创建方案失败:", err);
    await showAlert("错误", `创建方案失败: ${err.message || "未知错误"}`);
  }
};

/**
 * 部署方案
 * @param {Object} solution - 要部署的方案对象
 * @param {boolean} [incremental=true] - 是否使用增量部署
 * @returns {Promise<void>}
 */
const deploySolution = async (solution, incremental = true) => {
  // 如果没有传入solution参数，使用currentSolution
  const solutionToDeploy = solution || currentSolution.value;

  if (!solutionToDeploy) {
    await showAlert("错误", "请先选择一个方案");
    return;
  }

  console.log("开始部署方案:", solutionToDeploy.name, "增量部署:", incremental);

  try {
    // 获取设备列表
    const deviceList = await electronAPI.getDeviceHistory();

    if (!deviceList || deviceList.length === 0) {
      await showAlert("提示", "没有可用的设备");
      return;
    }

    // 处理设备列表，添加isAdded属性
    const devices = [];
    for (const device of deviceList) {
      const isAdded = await electronAPI.isDeviceAdded(device.sn);
      devices.push({
        sn: device.sn,
        id: device.id,
        isOnline: device.isOnline,
        isAdded: isAdded,
        status: device.status || {},
      });
    }

    // 过滤出已添加的设备
    const addedDevices = devices.filter((device) => device.isAdded);

    if (addedDevices.length === 0) {
      await showAlert("提示", "没有已添加的设备");
      return;
    }

    // 显示设备选择对话框
    const selectedDevices = ref([]);

    // 创建一个轻量级刷新设备的函数
    const refreshDeviceList = async () => {
      console.log("轻量级刷新设备列表");

      // 如果没有设备列表或对话框组件，跳过刷新
      if (!addedDevices || !DevicePanel || !DevicePanel.props) return;

      try {
        // 获取设备历史记录
        const historyDevices = await electronAPI.getDeviceHistory();

        if (!historyDevices || historyDevices.length === 0) return;

        // 更新现有设备的状态，而不是替换整个列表
        const updatedDevices = [...DevicePanel.props.devices];

        for (const newDevice of historyDevices) {
          const index = updatedDevices.findIndex((d) => d.sn === newDevice.sn);

          if (index !== -1) {
            // 只更新在线状态和状态信息，保留其他属性
            updatedDevices[index] = {
              ...updatedDevices[index],
              isOnline: newDevice.isOnline,
              status: newDevice.status || {},
            };
          } else {
            // 如果是新设备，检查是否已添加
            const isAdded = await electronAPI.isDeviceAdded(newDevice.sn);

            if (isAdded) {
              // 添加到设备列表
              updatedDevices.push({
                sn: newDevice.sn,
                id: newDevice.id,
                isOnline: newDevice.isOnline,
                isAdded: true,
                status: newDevice.status || {},
              });
            }
          }
        }

        // 检查是否有设备已被移除
        const currentSNs = historyDevices.map((d) => d.sn);
        const filteredDevices = updatedDevices.filter((d) =>
          currentSNs.includes(d.sn)
        );

        // 更新props中的设备列表
        DevicePanel.props.devices = filteredDevices;
      } catch (err) {
        console.error("刷新设备状态失败:", err);
      }
    };

    // 显示设备选择对话框
    const deployDialogResult = await alertService.customDialog({
      title: "选择部署设备",
      component: DevicePanel,
      props: {
        devices: addedDevices,
        modelValue: selectedDevices.value,
        disableOffline: true,
        title: "选择部署设备",
        emptyMessage: "暂无可用设备",
        showRefreshButton: true,
        showModeToggle: false,
        autoRefresh: false,
      },
      modelValue: selectedDevices,
      width: "500px",
      confirmButtonText: "开始部署",
      showCancelButton: false,
      showClose: true,
      onRefresh: refreshDeviceList,
    });

    if (deployDialogResult && selectedDevices.value.length > 0) {
      // 构建部署数据 - 确保只传递设备序列号数组
      const deviceSerialNumbers = selectedDevices.value.map(
        (device) => device.sn || device
      );

      const deployData = {
        devices: deviceSerialNumbers,
        uuid: solutionToDeploy.UUID,
        incremental: incremental, // 使用传入的增量部署参数
      };

      console.log("部署数据:", JSON.stringify(deployData));

      // 初始化设备进度
      for (const device of addedDevices) {
        // 为所有设备添加进度属性，默认为0
        device.deployProgress = 0;
      }

      // 显示部署进度对话框（不可关闭）
      alertService.customDialog({
        title: "正在部署...",
        component: DevicePanel,
        props: {
          devices: addedDevices,
          modelValue: selectedDevices.value,
          disableOffline: true,
          title: "部署进度",
          emptyMessage: "暂无可用设备",
          showRefreshButton: false,
          showModeToggle: false,
          autoRefresh: false,
        },
        modelValue: selectedDevices,
        width: "500px",
        confirmButtonText: "",
        cancelButtonText: "",
        showConfirmButton: false,
        showCancelButton: false,
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      });

      // 显示部署中状态
      loading.value = true;

      try {
        // 调用部署方法
        const deployResult = await electronAPI.deploySolution(deployData);

        // 关闭进度对话框
        alertService.closeDialog();

        // 显示部署结果
        await showAlert(
          "部署结果",
          `部署完成！\n成功：${deployResult.successful.length} 台设备\n失败：${deployResult.failed.length} 台设备`
        );
      } catch (error) {
        // 关闭进度对话框
        alertService.closeDialog();

        // 显示错误信息
        await showAlert(
          "部署失败",
          `部署过程中发生错误：${error.message || "未知错误"}`
        );
      }
    }
  } catch (err) {
    await handleError("部署方案失败", err);
  } finally {
    loading.value = false;
  }
};

/**
 * 离线部署（打开config文件夹）
 * @returns {Promise<void>}
 */
const exportSolution = async () => {
  if (!currentSolution.value) return;

  try {
    // 直接打开 DeviceManagerResources 文件夹
    await electronAPI.openResourcesFolder();
    console.log("打开资源文件夹成功");
  } catch (err) {
    await handleError("打开资源文件夹失败", err);
  }
};

/**
 * 切换菜单显示
 */
const toggleMenu = () => {
  // 不需要实现，因为 menuVisible 状态已移除
};

/**
 * 关闭菜单
 */
const closeMenu = () => {
  // 不需要实现，因为 menuVisible 状态已移除
};

/**
 * 确认删除方案
 * @returns {Promise<void>}
 */
const confirmDeleteSolution = async () => {
  if (!currentSolution.value) return;

  // 关闭菜单
  // 不需要实现，因为 menuVisible 状态已移除

  try {
    const result = await alertService.confirm({
      title: "删除方案",
      message: `确定要删除方案 "${currentSolution.value.name}" 吗？此操作不可恢复，方案中的所有资源将被删除。`,
      confirmButtonText: "删除",
      showCancelButton: false,
      type: "danger",
    });

    if (result) {
      await deleteSolution();
      showResourceManager.value = false; // 隐藏资源管理器
    }
  } catch (err) {
    await handleError("确认删除方案失败", err);
  }
};
/**
 * 删除方案
 * @returns {Promise<void>}
 */
const deleteSolution = async () => {
  if (!currentSolution.value) return;

  try {
    loading.value = true;

    // 调用API删除方案
    await electronAPI.deleteSolution(currentSolution.value.UUID);

    // 刷新方案列表
    await fetchSolutions();
    // 显示成功消息
    messageService.success("方案删除成功");
  } catch (err) {
    await handleError("删除方案失败", err);
  } finally {
    loading.value = false;
  }
};

/**
 * 分组管理相关方法已移除
 */

/**
 * 注意：以下方法已被注释，因为它们在当前UI中不再使用
 * 这些方法已被更新的内联编辑功能所取代
 */
// const openAddGroupDialog = () => {
//   editingGroup.value = null;
//   showGroupDialog.value = true;
// };

/**
 * 注意：以下方法已被注释，因为它们在当前UI中不再使用
 * 这些方法已被更新的内联编辑功能所取代
 */
// const openEditGroupDialog = (groupName) => {
//   editingGroup.value = {
//     name: groupName,
//     description: '' // 目前没有分组描述，可以后续添加
//   };
//   showGroupDialog.value = true;
// };
//
// const closeGroupDialog = () => {
//   showGroupDialog.value = false;
//   editingGroup.value = null;
// };
//
// const handleGroupDialogConfirm = async (groupData) => {
//   try {
//     loading.value = true;
//
//     if (editingGroup.value) {
//       // 编辑现有分组
//       await electronAPI.updateSolutionGroup(currentSolution.value.UUID, editingGroup.value.name, groupData.name, groupData.description);
//       await showAlert('成功', '分组更新成功');
//     } else {
//       // 添加新分组
//       await electronAPI.addSolutionGroup(currentSolution.value.UUID, groupData.name, groupData.description);
//       await showAlert('成功', '分组添加成功');
//     }
//
//     // 关闭对话框
//     closeGroupDialog();
//
//     // 刷新方案列表
//     await fetchSolutions();
//
//     // 刷新当前方案
//     if (currentSolution.value) {
//       const solution = solutions.value.find(s => s.UUID === currentSolution.value.UUID);
//       if (solution) {
//         currentSolution.value = solution;
//         // 如果是新添加的分组，自动选择它
//         if (!editingGroup.value) {
//           selectedGroup.value = groupData.name;
//         }
//       }
//     }
//   } catch (err) {
//     await handleError(editingGroup.value ? '更新分组失败' : '添加分组失败', err);
//   } finally {
//     loading.value = false;
//   }
// };

/**
 * 分组管理对话框事件处理已移除
 */

/**
 * 注意：以下方法已被注释，因为它在当前UI中不再使用
 * 刷新方案数据功能已集成到其他方法中
 */
// const refreshSolutionData = async () => {
//   // 刷新方案列表
//   await fetchSolutions();
//
//   // 刷新当前方案
//   if (currentSolution.value) {
//     const solution = solutions.value.find(s => s.UUID === currentSolution.value.UUID);
//     if (solution) {
//       currentSolution.value = solution;
//     }
//   }
// };

/**
 * 处理添加分组事件
 * @param {Object} groupData - 分组数据
 * @returns {Promise<void>}
 */
const handleAddGroup = async (groupData) => {
  if (!currentSolution.value) {
    messageService.warning("请先设置一个播控方案");
    return;
  }

  try {
    loading.value = true;

    // 如果是从内联添加分组/子分类
    if (groupData.name) {
      // 构建完整的分组名称
      let fullName = groupData.name;

      // 如果有父分组，则构建子分类名称
      if (groupData.parentGroup) {
        fullName = createFullGroupName(groupData.parentGroup, groupData.name);
      }

      // 直接调用API添加分组
      await electronAPI.addSolutionGroup(
        currentSolution.value.UUID,
        fullName,
        groupData.description || ""
      );
      messageService.success("分组添加成功");

      // 刷新方案列表
      await fetchSolutions();

      // 选择新添加的分组
      selectedGroup.value = fullName;
    } else {
      // 注意：此处应调用showAddGroupDialog，但该方法已被注释
      // 此处代码已不再使用，仅保留用于兼容性
      console.log("添加分组对话框功能已移至ResourceManager组件");
    }
  } catch (err) {
    await handleError("添加分组失败", err);
  } finally {
    loading.value = false;
  }
};

/**
 * 注意：以下方法已被注释，因为它在当前UI中不再使用
 * 添加分组功能已集成到ResourceManager组件中
 */
// const showAddGroupDialog = (options = {}) => {
//   editingGroup.value = null;
//
//   // 如果提供了父分组，则设置为添加子分类模式
//   if (options.parentGroup) {
//     // 在这里可以设置默认父分组
//     console.log('添加子分类，父分组:', options.parentGroup);
//   }
//
//   // 此变量已不存在，此方法已不再使用
//   // showGroupDialog.value = true;
// };

/**
 * 注意：以下方法已被注释，因为它在当前UI中不再使用
 * 删除分组功能已集成到ResourceManager组件中
 */
// const confirmDeleteGroup = async (groupName) => {
//   if (!currentSolution.value) return;
//
//   try {
//     // 检查是否为子分类
//     const isSubCat = isSubcategory(groupName);
//     const typeText = isSubCat ? '子分类' : '分组';
//
//     // 使用标准确认对话框
//     const result = await alertService.confirm({
//       title: `删除${typeText}`,
//       message: `确定要删除${typeText} "${groupName}" 吗？此操作不可撤销。`,
//       confirmButtonText: '删除',
//       cancelButtonText: '取消',
//       type: 'danger'
//     });
//
//     if (result) {
//       // 使用localStorage中保存的deleteResourcesWithGroup设置
//       const deleteResources = localStorage.getItem('deleteResourcesWithGroup') === 'true';
//       await deleteGroup(groupName, deleteResources);
//     }
//   } catch (err) {
//     await handleError('确认删除分组失败', err);
//   }
// };

/**
 * 删除分组
 * @param {string} groupName - 要删除的分组名称
 * @param {boolean} [deleteResources=false] - 是否同时删除资源
 * @returns {Promise<void>}
 */
const deleteGroup = async (groupName, deleteResources = false) => {
  if (!currentSolution.value) {
    messageService.warning("请先设置一个播控方案");
    return;
  }

  try {
    loading.value = true;

    // 使用已导入的isSubcategory函数
    const isSubCat = isSubcategory(groupName);

    // 确保 deleteResources 参数是布尔值
    const shouldDeleteResources = deleteResources === true;

    // 使用对象参数
    const apiParams = {
      solutionId: currentSolution.value.UUID,
      name: groupName,
      deleteResources: shouldDeleteResources,
    };

    // 调用API
    await electronAPI.deleteSolutionGroup(apiParams);

    // 显示成功提示
    let successMessage = `${isSubCat ? "子分类" : "分组"}删除成功`;
    if (shouldDeleteResources) {
      successMessage += "，相关资源已被删除";
    }
    messageService.success(successMessage);

    // 如果当前选中的是被删除的分组，切换到"全部"
    if (selectedGroup.value === groupName) {
      selectedGroup.value = "全部";
    }

    // 刷新方案列表
    await fetchSolutions();

    // 刷新当前方案
    if (currentSolution.value) {
      const solution = solutions.value.find(
        (s) => s.UUID === currentSolution.value.UUID
      );
      if (solution) {
        currentSolution.value = solution;
      }
    }
  } catch (err) {
    await handleError("删除分组失败", err);
  } finally {
    loading.value = false;
  }
};

/**
 * 显示添加资源对话框
 * @returns {Promise<void>}
 */
const showAddResourceDialog = async () => {
  if (!currentSolution.value) {
    messageService.warning("请先设置一个播控方案");
    return;
  }

  try {
    // 获取当前方案的所有分组
    const categories = currentSolution.value.groups || DEFAULT_GROUPS;

    // 显示资源对话框（添加模式）
    const newResource = await resourceDialogService.show({
      categories: categories,
      selectedCategory: selectedGroup.value,
    });

    // 如果用户取消了操作
    if (!newResource) {
      return;
    }

    // 显示资源区域加载状态
    resourceLoading.value = true;
    resourceLoadingText.value = "正在上传资源";

    try {
      // 显示资源上传等待页面
      // 使用带有加载状态的alert方法显示加载中对话框
      alertService.alert({
        title: "资源上传中",
        message: "正在上传资源，请稍候...",
        showConfirmButton: false,
        showCancelButton: false,
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        isLoading: true, // 启用加载状态
      });

      // 准备创建资源的数据
      const fileName =
        newResource.fileName ||
        (newResource.filePath ? getFileName(newResource.filePath) : "");
      const filePath = ensurePathString(newResource.filePath);
      const coverPath = ensurePathString(newResource.coverPath);

      // 根据后端 API 的预期格式构建请求数据
      const resourceData = {
        solutionId: currentSolution.value.UUID,
        resource: {
          showName: newResource.showName,
          describe: newResource.describe,
          path: filePath,
          poster: coverPath,
          type: newResource.type,
          fileName: fileName,
        },
        group: newResource.groups?.[0] || DEFAULT_GROUPS[0],
      };

      console.log("创建资源数据:", resourceData);

      // 调用 API 创建资源
      const createdResource = await electronAPI.createResource(resourceData);

      // 关闭等待对话框
      alertService.closeDialog();

      if (createdResource) {
        // 更新加载文本
        resourceLoadingText.value = "资源上传成功，正在刷新";

        // 刷新方案列表
        await fetchSolutions();

        messageService.success("资源添加成功");
      } else {
        throw new Error("创建资源失败");
      }
    } catch (err) {
      // 确保关闭等待对话框
      alertService.closeDialog();
      await handleError("添加资源失败", err);
    } finally {
      resourceLoading.value = false;
    }
  } catch (err) {
    await handleError("添加资源失败", err);
  }
};

/**
 * 从路径中获取文件名
 * @param {string|Object} path - 文件路径或包含路径的对象
 * @returns {string} 文件名
 */
const getFileName = (path) => {
  if (!path) return "";

  try {
    // 如果 path 是对象且有 name 属性，直接返回 name
    if (typeof path === "object" && path !== null) {
      if (path.name) {
        return path.name;
      }

      // 如果有 path 属性，尝试从中提取文件名
      if (path.path && typeof path.path === "string") {
        const parts = path.path.split(/[/\\]/);
        return parts[parts.length - 1] || "";
      }

      return "";
    }

    // 如果 path 是字符串，直接处理
    if (typeof path === "string") {
      // 处理 Windows 和 Unix 风格的路径
      const parts = path.split(/[/\\]/);
      return parts[parts.length - 1] || "";
    }

    return "";
  } catch (err) {
    console.error("获取文件名失败:", err);
    return "";
  }
};

/**
 * 编辑资源
 * @param {Object} resource - 要编辑的资源对象
 * @returns {Promise<void>}
 */
const editResource = async (resource) => {
  if (!currentSolution.value) return;

  try {
    // 获取当前方案的所有分组
    const categories = currentSolution.value.groups || DEFAULT_GROUPS;

    // 显示编辑资源对话框
    const updatedResource = await resourceDialogService.show({
      resource: resource,
      categories: categories,
      selectedCategory: selectedGroup.value,
    });

    // 如果用户保存了更新的资源
    if (updatedResource) {
      // 显示资源区域加载状态
      resourceLoading.value = true;
      resourceLoadingText.value = "正在更新资源";

      try {
        // 准备更新资源的数据，确保所有数据都是可序列化的
        const resourceData = {
          showName: updatedResource.showName,
          describe: updatedResource.describe,
          groups: updatedResource.groups,
          type: updatedResource.type,
          // 确保关键标识字段不变
          path: resource.path,
          MD5: resource.MD5,
          index: resource.index,
        };

        // 如果上传了新文件，更新文件相关字段
        if (
          updatedResource.filePath &&
          updatedResource.filePath !== resource.filePath
        ) {
          // 确保filePath是字符串
          resourceData.filePath =
            typeof updatedResource.filePath === "object"
              ? updatedResource.filePath.path ||
                updatedResource.filePath.name ||
                ""
              : String(updatedResource.filePath);

          // 确保fileName是字符串
          resourceData.fileName =
            typeof updatedResource.fileName === "string"
              ? updatedResource.fileName
              : getFileName(updatedResource.filePath);
        }

        // 如果上传了新封面，更新封面相关字段
        if (
          updatedResource.coverPath &&
          updatedResource.coverPath !== resource.coverPath
        ) {
          // 确保coverPath是字符串
          resourceData.coverPath =
            typeof updatedResource.coverPath === "object"
              ? updatedResource.coverPath.path ||
                updatedResource.coverPath.name ||
                ""
              : String(updatedResource.coverPath);
        }

        console.log("更新资源数据:", resourceData);

        // 确保资源对象包含必要的字段
        if (!resourceData.path) {
          console.error("资源缺少 path 字段:", resourceData);
          throw new Error("资源缺少必要的标识字段");
        }

        // 调用后端 API 更新资源
        await electronAPI.updateResource(resourceData);

        // 刷新方案列表
        await fetchSolutions();

        // 显示成功提示
        messageService.success("资源更新成功");
      } catch (error) {
        console.error("更新资源失败:", error);
        await showAlert("错误", `更新资源失败: ${error.message || "未知错误"}`);
      } finally {
        resourceLoading.value = false;
      }
    }
  } catch (error) {
    console.error("编辑资源失败:", error);
    await showAlert("错误", `编辑资源失败: ${error.message || "未知错误"}`);
  }
};

/**
 * 删除资源
 * @param {Object} resource - 要删除的资源对象
 * @returns {Promise<void>}
 */
const deleteResource = async (resource) => {
  try {
    console.log("删除资源:", resource);

    const confirmed = await alertService.confirm({
      title: "删除资源",
      message: `确定要删除资源 "${resource.showName || resource.fileName}" 吗？此操作不可恢复。`,
      confirmButtonText: "删除",
      cancelButtonText: "取消",
      type: "danger",
    });

    if (confirmed) {
      // 显示资源区域加载状态
      resourceLoading.value = true;
      resourceLoadingText.value = "正在删除资源";

      try {
        // 确保资源有路径
        if (!resource.path) {
          throw new Error("资源路径不存在");
        }

        console.log("删除资源路径:", resource.path);

        // 调用删除资源方法
        // 后端 ResourceController.deleteResource 方法期望接收资源路径或UUID
        await electronAPI.deleteResource(resource.path);

        // 更新加载文本
        resourceLoadingText.value = "资源删除成功，正在刷新";

        messageService.success("资源删除成功");

        // 刷新方案列表
        await fetchSolutions();
      } catch (err) {
        await handleError("删除资源失败", err);
      } finally {
        resourceLoading.value = false;
      }
    }
  } catch (err) {
    await handleError("删除资源失败", err);
  }
};

/**
 * 编辑方案
 * @returns {Promise<void>}
 */
const editSolution = async (solution) => {
  // 选择方案并进入资源管理页面
  currentSolution.value = solution;
  showResourceManager.value = true;
  // 设置默认选中第一个分组
  selectedGroup.value = solution.groups?.[0] || DEFAULT_GROUPS[0];
};

/**
 * 确保路径是字符串
 * @param {string|Object} path - 文件路径或包含路径的对象
 * @returns {string} 处理后的路径字符串
 */
const ensurePathString = (path) => {
  if (!path) return "";

  if (typeof path === "object" && path !== null) {
    return path.path || path.name || "";
  }

  return String(path);
};

/**
 * 显示提示对话框
 * @param {string} title - 标题
 * @param {string} message - 消息内容
 * @param {string} [buttonText='确定'] - 按钮文本
 * @returns {Promise<boolean>} 用户点击确定返回true
 */
const showAlert = async (title, message, buttonText = "确定") => {
  return await alertService.alert({
    title,
    message,
    confirmButtonText: buttonText,
  });
};

/**
 * 处理错误
 * @param {string} title - 错误标题
 * @param {Error} error - 错误对象
 * @returns {Promise<void>}
 */
const handleError = async (title, error) => {
  console.error(title, error);
  await showAlert("错误", `${title}: ${error.message || "未知错误"}`);
};

/**
 * 格式化日期
 * @param {string|number|Date} date - 日期
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = (date) => {
  if (!date) return "未知";
  return new Date(date).toLocaleString();
};

/**
 * 添加返回方案列表方法
 */
const backToSolutionList = () => {
  showResourceManager.value = false;
  currentSolution.value = null;
};

/**
 * 显示部署选项对话框
 */
const showDeployOptions = (solution) => {
  currentSolution.value = solution;
  showDeployDialog.value = true;
};

/**
 * 关闭部署选项对话框
 */
const closeDeployDialog = () => {
  showDeployDialog.value = false;
  // 不要 currentSolution.value = null;
};

/**
 * 处理部署选项
 */
const handleDeploy = async (type) => {
  if (!currentSolution.value) return;

  if (type === "online") {
    try {
      // 检查config文件夹大小
      const sizeCheck = await electronAPI.checkConfigFolderSize();
      if (sizeCheck.isOverLimit) {
        // 超过
        const sizeGB = (sizeCheck.size / (1024 * 1024 * 1024)).toFixed(2);
        await showAlert(
          "部署失败",
          `config文件夹大小(${sizeGB}GB)超过限制(10GB)，不允许在线部署`
        );
        return;
      }

      // 在线部署逻辑，默认使用增量部署
      onlineDeployment.value = true;
      showResourceManager.value = false;
    } catch (error) {
      await handleError("检查文件夹大小失败", error);
      return;
    }
  } else {
    // 离线部署逻辑
    if (!currentSolution.value) {
      messageService.warning("请先设置一个播控方案");
    } else {
      offlineDeployment.value = true;
      // exportSolution(currentSolution.value);
    }
  }
  closeDeployDialog();
};

// 获取背景图片URL
const updateBackgroundUrl = async (backgroundPath) => {
  if (!backgroundPath) {
    backgroundUrl.value = "";
    return;
  }
  try {
    backgroundUrl.value = await electronAPI.getResourceFilePath(backgroundPath);
  } catch (error) {
    console.error("获取背景图片失败:", error);
    backgroundUrl.value = "";
  }
};

// 监听当前方案变化
watch(
  () => currentSolution.value?.background,
  async (newBackground) => {
    await updateBackgroundUrl(newBackground);
    console.log("当前方案背景变化:", currentSolution.value);
  },
  { immediate: true }
);

// 计算背景样式
const contentPanelStyle = computed(() => {
  if (!showResourceManager.value || solutions.length === 0) {
    return { "--background-image": "none" };
  }
  return {
    "--background-image": `url(${backgroundUrl.value ? backgroundUrl.value : defaultBgImage})`,
  };
});

/**
 * 获取设备状态类名
 */
const getDeviceStatusClass = (device) => {
  switch (device.deployStatus) {
    case "completed":
      return "status-success";
    case "failed":
      return "status-error";
    case "deploying":
      return "status-deploying";
    default:
      return "status-pending";
  }
};

/**
 * 获取设备状态文本
 */
const getDeviceStatusText = (device) => {
  switch (device.deployStatus) {
    case "completed":
      return "已完成";
    case "failed":
      return "失败";
    case "deploying":
      return "部署中";
    default:
      return "等待中";
  }
};

/**
 * 获取进度条类名
 */
const getProgressClass = (device) => {
  switch (device.deployStatus) {
    case "completed":
      return "progress-success";
    case "failed":
      return "progress-error";
    case "deploying":
      return "progress-deploying";
    default:
      return "progress-pending";
  }
};

// 生命周期钩子
onMounted(async () => {
  await fetchSettings();
  fetchSolutions();
  // 注册进度事件监听
  transferHandler = electronAPI.onTransferProgress(handleTransferProgress);
  errorHandler = window.electronAPI.onDeployError((data) => {
    const dev = onlineDeployDevices.value.find((d) => d.sn === data.deviceSN);
    if (dev) {
      dev.deployStatus = "失败";
      dev.deployProgress = 0;
    }
  });
});

onUnmounted(() => {
  if (transferHandler) window.electronAPI.offTransferProgress(transferHandler);
  if (errorHandler) window.electronAPI.offDeployError(errorHandler);
  // 清除所有定时器
  configTransferTimers.forEach((timer) => clearTimeout(timer));
  configTransferTimers.clear();
});

/**
 * 从部署进度页面返回
 */
const backFromDeploy = () => {
  if (onlineDeployment.value) {
    if (deployStore.isDeploying) {
      messageService.warning(
        "有设备正在部署中，请等待部署完成或取消部署后再返回"
      );
      return;
    }
    onlineDeployment.value = false;
    currentSolution.value = null;
    return;
  }
  if (offlineDeployment) {
    offlineDeployment.value = false;
    return;
  }
};

/**
 * 开始部署方案
 */
const startDeploy = async () => {
  if (!currentSolution.value || !selectedDevices.value.length) return;

  try {
    // 构建部署数据
    const deviceSerialNumbers = selectedDevices.value.map(
      (device) => device.sn
    );
    const deployData = {
      devices: deviceSerialNumbers,
      uuid: currentSolution.value.UUID,
      incremental: true,
    };

    console.log("开始部署方案:", deployData);

    // 显示部署中状态
    loading.value = true;

    try {
      // 调用部署方法
      const deployResult = await electronAPI.deploySolution(deployData);

      // 更新设备状态
      for (const device of selectedDevices.value) {
        if (deployResult.successful.includes(device.sn)) {
          device.deployStatus = "completed";
          device.deployProgress = 100;
          completedDevices.value.push(device);
        } else if (deployResult.failed.includes(device.sn)) {
          device.deployStatus = "failed";
          failedDevices.value.push(device);
        }
      }

      // 显示部署结果
      await showAlert(
        "部署结果",
        `部署完成！\n成功：${deployResult.successful.length} 台设备\n失败：${deployResult.failed.length} 台设备`
      );
    } catch (error) {
      // 更新所有设备状态为失败
      for (const device of selectedDevices.value) {
        device.deployStatus = "failed";
        failedDevices.value.push(device);
      }

      // 显示错误信息
      await showAlert(
        "部署失败",
        `部署过程中发生错误：${error.message || "未知错误"}`
      );
    }
  } catch (err) {
    await handleError("部署方案失败", err);
  } finally {
    loading.value = false;
  }
};

// 获取设备数据（可在handleDeploy或onMounted中调用）
const fetchOnlineDeployDevices = async () => {
  const deviceList = await electronAPI.getDeviceHistory();
  const devices = [];
  for (const device of deviceList) {
    const isAdded = await electronAPI.isDeviceAdded(device.sn);
    if (isAdded) {
      devices.push({
        deviceNo: device.id || "",
        sn: device.sn,
        model: device.model || "",
        status: device.isOnline ? "在线" : "离线",
        deployStatus: device.deployStatus || "未部署",
        deployTime: device.deployTime || "",
        isOnline: device.isOnline,
      });
    }
  }
  onlineDeployDevices.value = devices;
};

// 处理单个部署
const handleDeployOne = async (row) => {
  deployStore.startDeploy(row.sn);
  row.deployStatus = "部署中";
  row.deployProgress = 0;
  const deployData = {
    devices: [row.sn],
    uuid: currentSolution.value?.UUID,
    incremental: true,
  };
  console.log("部署数据:", JSON.stringify(deployData));
  try {
    await electronAPI.deploySolution(deployData);
    // 进度和状态由事件驱动
  } catch (e) {
    deployStore.cancelDeploy(row.sn);
    row.deployStatus = "失败";
    row.deployProgress = 0;
  }
};
// 处理单个取消部署
const handleCancelDeployOne = async (row) => {
  try {
    console.log("取消部署设备:", row.sn);
    deployStore.cancelDeploy(row.sn);
    // 立即更新设备状态为失败
    row.deployStatus = "失败";
    row.deployProgress = 0;
    row.deployFileIndex = undefined;
    row.deployFileTotal = undefined;
    // 清除该设备的定时器
    clearConfigTimer(row.sn);
    await electronAPI.cancelDeploy(row.sn);
  } catch (error) {
    console.error("取消部署失败:", error);
    messageService.error("取消部署失败: " + (error.message || "未知错误"));
  }
};
// 处理批量部署
const handleBatchDeploy = async (rows) => {
  rows.forEach((row) => {
    deployStore.startDeploy(row.sn);
    row.deployStatus = "部署中";
    row.deployProgress = 0;
  });
  const deployData = {
    devices: rows.map((r) => r.sn),
    uuid: currentSolution.value?.UUID,
    incremental: true,
  };
  console.log("部署数据:", JSON.stringify(deployData));
  try {
    await electronAPI.deploySolution(deployData);
    // 进度和状态由事件驱动
  } catch (e) {
    rows.forEach((row) => {
      deployStore.cancelDeploy(row.sn);
      row.deployStatus = "失败";
      row.deployProgress = 0;
    });
  }
};

// 在显示部署面板时加载设备数据
watch(
  () => onlineDeployment.value,
  (val) => {
    if (val) {
      console.log(
        "进入在线部署页面时的 currentSolution:",
        currentSolution.value
      );
      fetchOnlineDeployDevices();
    }
  }
);

const configTransferTimers = new Map(); // 用于存储每个设备的定时器

const clearConfigTimer = (deviceSN) => {
  if (configTransferTimers.has(deviceSN)) {
    clearTimeout(configTransferTimers.get(deviceSN));
    configTransferTimers.delete(deviceSN);
  }
};

// 监听进度事件
const handleTransferProgress = (data) => {
  const device = onlineDeployDevices.value.find((d) => d.sn === data.deviceSN);
  if (device) {
    // 如果设备已经被取消部署（状态为失败），完全忽略所有进度更新
    if (device.deployStatus === "失败") {
      console.log("设备已取消部署，忽略进度更新:", data.deviceSN);
      return;
    }

    // 清除之前的定时器
    clearConfigTimer(data.deviceSN);

    // 更新进度
    if (
      typeof data.totalFiles === "number" &&
      typeof data.fileIndex === "number"
    ) {
      // 如果有总文件数，计算整体进度
      const fileProgress = data.progress || 0;
      const currentFileProgress =
        (data.fileIndex + fileProgress) / data.totalFiles;
      device.deployProgress = Math.round(currentFileProgress * 100);
    } else {
      // 如果没有总文件数，直接使用当前文件进度
      device.deployProgress = Math.round((data.progress || 0) * 100);
    }

    device.deployFileName = data.fileName || "";
    device.deployFileIndex =
      typeof data.fileIndex === "number" ? data.fileIndex : undefined;
    device.deployFileTotal =
      typeof data.totalFiles === "number" ? data.totalFiles : undefined;

    // 打印传输进度信息
    console.log("传输进度信息:", {
      deviceSN: data.deviceSN,
      fileName: data.fileName,
      progress: data.progress,
      fileIndex: data.fileIndex,
      totalFiles: data.totalFiles,
      deployStatus: device.deployStatus,
    });

    // 修改判断逻辑：
    // 1. 当totalFiles和fileIndex都是undefined且progress=1时，表示只有config.dat文件
    // 2. 有多个文件时，需要等所有文件传输完成才显示成功
    if (data.progress === 1) {
      if (
        typeof data.totalFiles === "undefined" &&
        typeof data.fileIndex === "undefined"
      ) {
        // 设置2秒定时器，如果没有新的进度更新则显示成功
        const timer = setTimeout(() => {
          // 再次检查设备状态，确保没有被取消
          if (device.deployStatus !== "失败") {
            device.deployStatus = "部署成功";
            // 更新部署时间
            device.deployTime = new Date().toLocaleString();
            // 更新部署中的设备数量
            deployStore.finishDeploy(device.sn);
          }
        }, 2000);
        configTransferTimers.set(data.deviceSN, timer);
      } else if (
        typeof data.fileIndex === "number" &&
        typeof data.totalFiles === "number" &&
        data.fileIndex + 1 === data.totalFiles
      ) {
        // 再次检查设备状态，确保没有被取消
        if (device.deployStatus !== "失败") {
          device.deployStatus = "部署成功";
          // 更新部署时间
          device.deployTime = new Date().toLocaleString();
          // 更新部署中的设备数量
          deployStore.finishDeploy(device.sn);
        }
      } else {
        // 再次检查设备状态，确保没有被取消
        if (device.deployStatus !== "失败") {
          device.deployStatus = "部署中";
        }
      }
    } else {
      // 再次检查设备状态，确保没有被取消
      if (device.deployStatus !== "失败") {
        device.deployStatus = "部署中";
      }
    }
  }
};

onMounted(() => {
  // 注册进度事件监听
  transferHandler = electronAPI.onTransferProgress(handleTransferProgress);
});

onUnmounted(() => {
  if (transferHandler) window.electronAPI.offTransferProgress(transferHandler);
  if (errorHandler) window.electronAPI.offDeployError(errorHandler);
  // 清除所有定时器
  configTransferTimers.forEach((timer) => clearTimeout(timer));
  configTransferTimers.clear();
});
</script>

<style scoped>
.page {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  margin: 0;
  padding: 0;
  padding-top: 88px;

  background-color: var(--color-background-light);
}

.header {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 2;
  margin-bottom: var(--spacing-sm);
  height: 88px;
}

.header-title {
  color: var(--color-menu-text);
  text-align: center;
  font-size: 24px;
  font-style: normal;
  font-weight: 480;
  line-height: 18px; /* 75% */
  padding: 34px 0 0 34px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-desc {
  color: var(--color-white);
  font-size: 12px;
  font-style: normal;
  font-weight: 250;
  line-height: 18px;
  cursor: pointer;
}
.header-desc-title {
  font-size: 24px;
  font-style: normal;
  font-weight: 480;
  line-height: 18px;
  margin-bottom: 11px;
}

.header-desc-title img {
  margin-left: 24px;
  cursor: pointer;
  transition: opacity 0.3s;
}

.header-desc-title img:hover {
  opacity: 0.8;
}

.header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
  padding-bottom: 4px;
}
.header-actions {
  display: flex;
  justify-content: space-between;
  width: calc(100vw - 92px);
  background: linear-gradient(
    90deg,
    rgba(51, 52, 61, 0.4) 0%,
    rgba(124, 126, 146, 0.3) 100%
  );
  height: 88px;
  padding: 10px 34px;
  border-radius: var(--border-radius) 0 0 0;
}
.header-top {
  display: flex;
  justify-content: space-between;
  width: calc(100vw - 92px);
  background:;
  height: 88px;
  padding: 10px 30px;
  border-radius: var(--border-radius) 0 0 0;
}
.header-top img {
  margin-right: 24px;
}
.header-top.online {
  background-color: white;
}

.content-panel {
  z-index: 1;
  flex: 1;
  overflow: auto;
  background-color: var(--color-background-light);
  border-radius: var(--border-radius) 0 0 0;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-md);
  justify-content: center;
  height: 100%;
}

.content-panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: var(--background-image, none);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.8;
  border-radius: var(--border-radius) 0 0 0;
}

.content-panel > * {
  position: relative;
  z-index: 1;
}

.loading,
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  text-align: center;
  height: 100%;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-icon {
  font-size: 48px;
  color: var(--color-text-disabled);
  margin-bottom: var(--spacing-md);
}

.solution-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 100px;
}

/* 方案信息卡片样式 */
.solution-info-card {
  background-color: var(--color-card-background-online);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--color-primary);
}

.solution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.solution-title {
  flex: 1;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-sm);
}

.solution-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.solution-name {
  cursor: pointer;
  transition: color 0.3s;
  position: relative;
}

.solution-name:hover {
  color: var(--color-primary);
}

.solution-name::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--color-primary);
  transition: width 0.3s;
}

.solution-name:hover::after {
  width: 100%;
}

.title-actions {
  display: flex;
  align-items: center;
}

.menu-wrapper {
  position: relative;
}

/* 移除不再需要的样式 */

/* ResourceManager 组件将使用自己的样式 */

/* 移除不再需要的样式 */
.solution-content {
  display: none;
}

/* 分组和资源区域样式 */
.solution-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--color-card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
}

/* 分组标签栏已删除，使界面更加简洁 */

/* 资源管理区域样式 */
.resource-management {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: var(--spacing-md);
  background-color: var(--color-background-light);
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  border-bottom: 1px solid var(--color-border-light);
}

.resource-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  background-color: var(--color-background);
  border-radius: var(--border-radius);
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
}

/* 调整搜索框和筛选器样式 */
.search-input {
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.filter-select {
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: all 0.3s ease;
}

.filter-select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

/* 调整空状态样式 */
.empty-list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: var(--spacing-md);
}

/* 调整加载状态样式 */
.loading {
  margin: var(--spacing-lg);
  padding: var(--spacing-xl);
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

/* 调整错误状态样式 */
.error {
  margin: var(--spacing-lg);
  padding: var(--spacing-xl);
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

/* 按钮样式美化 */
.btn {
  border-radius: var(--border-radius-md);
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  color: var(--color-white);
  width: 160px;
  height: 30px;
  font-size: 12px;
  font-style: normal;
  font-weight: 340;
  line-height: 18px;
  margin-right: 0;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
}

.btn-secondary:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* btn-text 样式引用自 DevicePanel.vue 组件 */
.btn-text {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  transition: all 0.2s;
}

.btn-text:hover {
  color: var(--color-primary);
  background-color: rgba(0, 0, 0, 0.05);
}

/* 暗色主题下的文本按钮 */
[data-theme="dark"] .btn-text {
  color: var(--color-text-secondary);
}

[data-theme="dark"] .btn-text:hover {
  color: var(--color-primary-light);
  background-color: rgba(255, 255, 255, 0.1);
}

/* 暗色主题下的文本按钮中的图标 */
[data-theme="dark"] .btn-text .icon-refresh {
  filter: brightness(0) invert(1);
}

.toggle-details {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border-radius: var(--border-radius-sm);
  transition: all 0.3s ease;
}

.btn i {
  margin-right: var(--spacing-xs);
}

/* 调整头部操作区域样式 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 5px; /* 更小的间距，与其他页面保持一致 */
}

/* 添加危险按钮样式 */
.btn-danger {
  background-color: var(--color-danger);
  color: white;
  border: none;
}

.btn-danger:hover {
  background-color: var(--color-danger-dark, #d32f2f);
}

.btn-danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.solution-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  justify-content: center;
  align-items: flex-start;
  overflow-y: auto;
  padding: var(--spacing-md);
}

/* 删除以下重复的样式 */
.solution-card,
.solution-card-header,
.solution-info,
.solution-name,
.solution-meta,
.solution-card-actions {
  /* 删除这些样式，因为它们已经在 SolutionCard 组件中定义 */
}

.deploy-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.deploy-dialog {
  background-color: var(--color-background);
  border-radius: var(--border-radius-lg);
  width: 480px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 34px;

  background-color: var(--color-card-background-online);
  width: 50vw;

  position: relative;
}

.deploy-dialog-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: center;
}

.deploy-dialog-header h3 {
  margin: 0;
  font-size: 1.2em;
  color: var(--color-text-primary);
}

.btn-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  width: 28px;
  height: 28px;
  transition: all 0.2s ease;
}

.btn-close:hover {
  background-color: var(--color-box-bg);
}

.deploy-dialog-content {
  padding: var(--spacing-md);
}

.deploy-option {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-md);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: var(--spacing-md);
  background-color: #d9e8f8;

  color: #595b6a;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
}

.deploy-option:last-child {
  margin-bottom: 0;
}

.deploy-option:hover {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.deploy-option i {
  font-size: 24px;
  color: var(--color-primary);
  margin-right: var(--spacing-md);
  margin-top: 2px;
}

.deploy-option-info {
  flex: 1;
  cursor: pointer;
}

.deploy-option-info h4 {
  margin: 0 0 var(--spacing-xs);
  color: var(--color-text-primary);
}

.deploy-option-info pre {
  cursor: pointer;
}

[data-theme="dark"] .deploy-dialog {
  background-color: var(--color-background-dark);
}

[data-theme="dark"] .deploy-dialog-header {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .deploy-option {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .deploy-option:hover {
  background-color: rgba(var(--color-primary-rgb), 0.1);
}
.dialog-title {
  color: var(---1, #595b6a);
  text-align: center;
  font-family: FZVariable-LanTingHeiK;
  font-size: 24px;
  font-style: normal;
  font-weight: 480;
  line-height: 18px;
  width: 100%;
  text-align: center;
}
.solution-actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.header-desc-content {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.3s;
}

.header-desc-content:hover {
  opacity: 0.8;
}

.desc-edit-icon {
  width: 16px;
  height: 16px;
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.header-desc-content:hover .desc-edit-icon {
  opacity: 1;
}

.edit-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.edit-input,
.edit-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 14px;
  color: var(--color-text-primary);
  background-color: var(--color-background-light);
  transition: all 0.3s;
}

.edit-input:focus,
.edit-textarea:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
}

.edit-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 部署进度页面样式 */
.deploy-progress-panel {
  position: absolute;
  top: 88px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-background);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  z-index: 1;
}

.deploy-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border-light);
  gap: var(--spacing-lg);
}

.deploy-progress-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: var(--color-text-primary);
  flex: 1;
}

.deploy-progress-stats {
  display: flex;
  gap: var(--spacing-xl);
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-label {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.stat-value {
  font-size: 24px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.deploy-progress-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  background-color: var(--color-background-light);
  border-radius: var(--border-radius);
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.device-item {
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.device-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.device-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.device-status {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
}

.status-success {
  color: var(--color-success);
  background-color: var(--color-success-light);
}

.status-error {
  color: var(--color-danger);
  background-color: var(--color-danger-light);
}

.status-deploying {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.status-pending {
  color: var(--color-text-secondary);
  background-color: var(--color-background-light);
}

.progress-bar {
  height: 8px;
  background-color: var(--color-background-light);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-success {
  background-color: var(--color-success);
}

.progress-error {
  background-color: var(--color-danger);
}

.progress-deploying {
  background-color: var(--color-primary);
}

.progress-pending {
  background-color: var(--color-text-secondary);
}

/* 暗色主题适配 */
[data-theme="dark"] .deploy-progress-panel {
  background-color: var(--color-background-dark);
}

[data-theme="dark"] .deploy-progress-content {
  background-color: var(--color-background);
}

[data-theme="dark"] .device-item {
  background-color: var(--color-background-dark);
}

[data-theme="dark"] .progress-bar {
  background-color: var(--color-background);
}
</style>
<style>
.edit-form .form-group {
  margin-bottom: 16px;
}

.edit-input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  border: none;
  margin-top: 24px;
  color: var(--color-menu-text);
  transition: border-color 0.3s;
  background-color: var(--color-dialog-background);
}

.edit-textarea {
  width: 100%;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
  resize: vertical;
  min-height: 80px;

  color: var(--color-menu-text);
  background-color: var(--color-dialog-background);
}
.back-icon {
}
</style>
